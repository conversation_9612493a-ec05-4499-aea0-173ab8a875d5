const express = require('express');
const router = express.Router();
const { pool } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

console.log('Department Permissions routes loaded');

// ==================== الحصول على جميع الإدارات ====================
router.get('/departments', authenticateToken, async (req, res) => {
  try {
    const [departments] = await pool.promise().query(`
      SELECT 
        id,
        name,
        description,
        is_active,
        created_at
      FROM departments 
      WHERE is_active = 1
      ORDER BY name
    `);

    res.json({
      success: true,
      data: departments
    });
  } catch (error) {
    console.error('خطأ في جلب الإدارات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإدارات'
    });
  }
});

// ==================== الحصول على صلاحيات إدارات مستخدم معين ====================
router.get('/user/:userId/departments', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // التحقق من وجود المستخدم وإعداداته
    const [userInfo] = await pool.promise().query(`
      SELECT 
        id,
        username,
        view_all_departments
      FROM users 
      WHERE id = ?
    `, [userId]);

    if (userInfo.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userInfo[0];

    // إذا كان المستخدم يرى جميع الإدارات
    if (user.view_all_departments) {
      const [allDepartments] = await pool.promise().query(`
        SELECT 
          d.id,
          d.name,
          d.description,
          1 as can_view,
          1 as can_manage
        FROM departments d
        WHERE d.is_active = 1
        ORDER BY d.name
      `);

      return res.json({
        success: true,
        data: {
          user: user,
          view_all_departments: true,
          departments: allDepartments
        }
      });
    }

    // الحصول على الإدارات المحددة للمستخدم
    const [userDepartments] = await pool.promise().query(`
      SELECT 
        d.id,
        d.name,
        d.description,
        COALESCE(udp.can_view, 0) as can_view,
        COALESCE(udp.can_manage, 0) as can_manage
      FROM departments d
      LEFT JOIN user_department_permissions udp ON d.name = udp.department AND udp.user_id = ?
      WHERE d.is_active = 1
      ORDER BY d.name
    `, [userId]);

    res.json({
      success: true,
      data: {
        user: user,
        view_all_departments: false,
        departments: userDepartments
      }
    });

  } catch (error) {
    console.error('خطأ في جلب صلاحيات الإدارات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب صلاحيات الإدارات'
    });
  }
});

// ==================== تحديث صلاحيات إدارات مستخدم ====================
router.put('/user/:userId/departments', authenticateToken, async (req, res) => {
  console.log('PUT /user/:userId/departments called with userId:', req.params.userId);
  console.log('Request body:', req.body);

  try {
    const { userId } = req.params;
    const { view_all_departments, departments } = req.body;

    // التحقق من صلاحية المستخدم الحالي
    // جلب بيانات المستخدم من قاعدة البيانات
    const [userRows] = await pool.promise().query(
      'SELECT username, permissions FROM users WHERE id = ?',
      [req.user.id]
    );

    if (userRows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userRows[0];

    // السماح للمستخدم admin دائماً
    if (user.username !== 'admin') {
      let userPermissions = {};
      if (user.permissions) {
        userPermissions = typeof user.permissions === 'string'
          ? JSON.parse(user.permissions)
          : user.permissions;
      }

      if (!userPermissions.manage_department_permissions) {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية لإدارة صلاحيات الإدارات'
        });
      }
    }

    // بدء المعاملة
    const connection = await pool.promise().getConnection();
    await connection.beginTransaction();

    try {
      // تحديث إعداد رؤية جميع الإدارات
      await connection.query(`
        UPDATE users 
        SET view_all_departments = ?
        WHERE id = ?
      `, [view_all_departments ? 1 : 0, userId]);

      // حذف الصلاحيات الحالية
      await connection.query(`
        DELETE FROM user_department_permissions 
        WHERE user_id = ?
      `, [userId]);

      // إذا لم يكن المستخدم يرى جميع الإدارات، أضف الصلاحيات المحددة
      if (!view_all_departments && departments && departments.length > 0) {
        const values = departments.map(dept => [
          userId,
          dept.name,
          dept.can_view ? 1 : 0,
          dept.can_manage ? 1 : 0
        ]);

        await connection.query(`
          INSERT INTO user_department_permissions 
          (user_id, department, can_view, can_manage)
          VALUES ?
        `, [values]);
      }

      await connection.commit();

      res.json({
        success: true,
        message: 'تم تحديث صلاحيات الإدارات بنجاح'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('خطأ في تحديث صلاحيات الإدارات:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث صلاحيات الإدارات'
    });
  }
});

// ==================== الحصول على الإدارات المسموح للمستخدم برؤيتها ====================
router.get('/user/allowed-departments', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // التحقق من إعداد رؤية جميع الإدارات
    const [userInfo] = await pool.promise().query(`
      SELECT view_all_departments 
      FROM users 
      WHERE id = ?
    `, [userId]);

    if (userInfo.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    let allowedDepartments = [];

    if (userInfo[0].view_all_departments) {
      // إذا كان يرى جميع الإدارات
      const [allDepartments] = await pool.promise().query(`
        SELECT DISTINCT name 
        FROM departments 
        WHERE is_active = 1
        ORDER BY name
      `);
      allowedDepartments = allDepartments.map(dept => dept.name);
    } else {
      // الحصول على الإدارات المحددة
      const [userDepartments] = await pool.promise().query(`
        SELECT DISTINCT department as name
        FROM user_department_permissions 
        WHERE user_id = ? AND can_view = 1
      `, [userId]);
      allowedDepartments = userDepartments.map(dept => dept.name);
    }

    res.json({
      success: true,
      data: allowedDepartments
    });

  } catch (error) {
    console.error('خطأ في جلب الإدارات المسموحة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإدارات المسموحة'
    });
  }
});

// ==================== إضافة إدارة جديدة ====================
router.post('/departments', authenticateToken, async (req, res) => {
  try {
    const { name, description } = req.body;

    // التحقق من صلاحية المستخدم
    const userPermissions = req.user.permissions;
    if (!userPermissions.manage_department_permissions) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لإدارة الإدارات'
      });
    }

    // التحقق من عدم وجود الإدارة مسبقاً
    const [existing] = await pool.promise().query(`
      SELECT id FROM departments WHERE name = ?
    `, [name]);

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'الإدارة موجودة مسبقاً'
      });
    }

    // إضافة الإدارة الجديدة
    const [result] = await pool.promise().query(`
      INSERT INTO departments (name, description)
      VALUES (?, ?)
    `, [name, description || `إدارة ${name}`]);

    res.json({
      success: true,
      message: 'تم إضافة الإدارة بنجاح',
      data: {
        id: result.insertId,
        name: name,
        description: description
      }
    });

  } catch (error) {
    console.error('خطأ في إضافة الإدارة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إضافة الإدارة'
    });
  }
});

// ==================== تعديل إدارة ====================
router.put('/departments/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    // التحقق من صلاحية المستخدم
    const userPermissions = req.user.permissions;
    if (!userPermissions.manage_department_permissions) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لإدارة الإدارات'
      });
    }

    // التحقق من وجود الإدارة
    const [existing] = await pool.promise().query(`
      SELECT id, name FROM departments WHERE id = ?
    `, [id]);

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الإدارة غير موجودة'
      });
    }

    // التحقق من عدم تكرار الاسم (إذا تم تغييره)
    if (name !== existing[0].name) {
      const [nameExists] = await pool.promise().query(`
        SELECT id FROM departments WHERE name = ? AND id != ?
      `, [name, id]);

      if (nameExists.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'اسم الإدارة موجود مسبقاً'
        });
      }
    }

    // تحديث الإدارة
    const [result] = await pool.promise().query(`
      UPDATE departments
      SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [name, description || `إدارة ${name}`, id]);

    // إذا تم تغيير اسم الإدارة، تحديث جدول الصلاحيات
    if (name !== existing[0].name) {
      await pool.promise().query(`
        UPDATE user_department_permissions
        SET department = ?
        WHERE department = ?
      `, [name, existing[0].name]);

      // تحديث جدول الموظفين أيضاً
      await pool.promise().query(`
        UPDATE employees
        SET department = ?
        WHERE department = ?
      `, [name, existing[0].name]);
    }

    res.json({
      success: true,
      message: 'تم تحديث الإدارة بنجاح',
      data: {
        id: id,
        name: name,
        description: description
      }
    });

  } catch (error) {
    console.error('خطأ في تحديث الإدارة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث الإدارة'
    });
  }
});

// ==================== حذف إدارة ====================
router.delete('/departments/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // التحقق من صلاحية المستخدم
    const userPermissions = req.user.permissions;
    if (!userPermissions.manage_department_permissions) {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لإدارة الإدارات'
      });
    }

    // التحقق من وجود الإدارة
    const [existing] = await pool.promise().query(`
      SELECT id, name FROM departments WHERE id = ?
    `, [id]);

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الإدارة غير موجودة'
      });
    }

    const departmentName = existing[0].name;

    // التحقق من وجود موظفين مرتبطين بهذه الإدارة
    const [employeesCount] = await pool.promise().query(`
      SELECT COUNT(*) as count FROM employees WHERE department = ?
    `, [departmentName]);

    if (employeesCount[0].count > 0) {
      return res.status(400).json({
        success: false,
        message: `لا يمكن حذف الإدارة لأنها مرتبطة بـ ${employeesCount[0].count} موظف. يرجى نقل الموظفين لإدارة أخرى أولاً.`
      });
    }

    // بدء المعاملة
    const connection = await pool.promise().getConnection();
    await connection.beginTransaction();

    try {
      // حذف صلاحيات الإدارة من جدول المستخدمين
      await connection.query(`
        DELETE FROM user_department_permissions
        WHERE department = ?
      `, [departmentName]);

      // حذف الإدارة (soft delete)
      await connection.query(`
        UPDATE departments
        SET is_active = 0, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [id]);

      await connection.commit();

      res.json({
        success: true,
        message: 'تم حذف الإدارة بنجاح'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('خطأ في حذف الإدارة:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في حذف الإدارة'
    });
  }
});

module.exports = router;
