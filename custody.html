<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام العهد</title>
  <link rel="stylesheet" href="shared-styles.css" />
  <link rel="stylesheet" href="style.css" />
  <link rel="stylesheet" href="custody.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="no-sidebar-layout.css">

  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
  <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">

  <script src="permissions.js" defer></script>
</head>
<body class="custody-page">

    <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="custody-cards.html" class="dashboard-btn">
      <i class="fas fa-arrow-right"></i>
      <span>العودة لإدارة العهد</span>
    </a>
  </div>





  <div class="main-content full-width" id="mainContent">
    <h1>نظام العهد</h1>



    <!-- إضافة عهدة -->
    <div class="tab-content" id="add-custody" style="display: none;">
      <div class="add-custody-section">
        <div class="form-toggle-container">
          <button class="compact-toggle-btn add-btn" id="toggleCustodyFormBtn">
            <i class="fas fa-plus-circle" id="toggleCustodyIcon"></i>
            <span id="toggleCustodyText">إضافة عهدة جديدة</span>
            <i class="fas fa-chevron-down toggle-arrow" id="toggleCustodyArrow"></i>
          </button>
        </div>
        <div id="custodyFormContainer" style="display: none;">
          <form id="addCustodyForm" class="custody-form form-grid">
        <!-- إخفاء حقل كود العهدة من خانة الإدخال -->
        <input type="hidden" id="custodyCode" name="custodyCode">
        <div class="form-group">
          <label for="custodyName">اسم العهدة:</label>
          <input type="text" id="custodyName" name="custodyName" required>
        </div>
        <div class="form-group">
          <label for="custodyType">نوع العهدة:</label>
          <input type="text" id="custodyType" name="custodyType" required>
        </div>
        <div class="form-group">
          <label for="custodyStatus">حالة العهدة:</label>
          <select id="custodyStatus" required>
              <option value="">اختر الحالة</option>
              <option value="جديدة">جديدة</option>
              <option value="مستعمل">مستعمل</option>
              <option value="صيانة">صيانة</option>
            </select>
        </div>
        <div class="form-group">
          <label for="custodyQuantity">العدد:</label>
          <input type="number" id="custodyQuantity" name="custodyQuantity" min="1" required>
        </div>

        <!-- أزرار التحكم تحت الحقول -->
        <div class="form-actions-bottom">
          <button type="submit" class="save-btn">حفظ العهدة</button>
          <button type="button" class="reset-btn">إعادة تعيين</button>
        </div>
        </form>
        </div>
      </div>

      <div class="custody-table-container">
        <h3>قائمة العهد</h3>

        <!-- فلاتر البحث المتقدمة -->
        <div class="form-toggle-container">
          <button class="compact-toggle-btn filter-btn" id="toggleCustodyFiltersBtn">
            <i class="fas fa-filter" id="toggleCustodyFiltersIcon"></i>
            <span id="toggleCustodyFiltersText">فلاتر البحث المتقدمة</span>
            <i class="fas fa-chevron-down toggle-arrow" id="toggleCustodyFiltersArrow"></i>
          </button>
        </div>
        <div class="advanced-search-container" id="custodyFiltersContainer" style="display: none;">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="searchCustodyCode">كود العهد:</label>
              <input type="text" id="searchCustodyCode" placeholder="ابحث بكود العهد" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchCustodyName">اسم العهدة:</label>
              <input type="text" id="searchCustodyName" placeholder="ابحث باسم العهدة" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchCustodyType">نوع العهدة:</label>
              <input type="text" id="searchCustodyType" placeholder="ابحث بنوع العهدة" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchCustodyStatus">الحالة:</label>
              <select id="searchCustodyStatus" class="filter-input">
                <option value="">جميع الحالات</option>
                <option value="جديدة">جديدة</option>
                <option value="مستعمل">مستعمل</option>
                <option value="صيانة">صيانة</option>
              </select>
            </div>

            <div class="form-group">
              <label for="searchDateFrom">تاريخ الإضافة من:</label>
              <input type="date" id="searchDateFrom" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchDateTo">تاريخ الإضافة إلى:</label>
              <input type="date" id="searchDateTo" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="applyAdvancedCustodySearch" class="search-btn">تطبيق البحث</button>
            <button id="clearAdvancedCustodySearch" class="reset-btn">مسح البحث</button>
          </div>
        </div>

        <!-- خيارات عرض جدول العهد -->
        <div class="table-controls" style="margin-bottom: 15px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; align-items: center; gap: 10px;">
              <label for="custodyEntriesPerPage" style="font-weight: 600; color: #512da8;">عدد الخانات في الصفحة:</label>
              <select id="custodyEntriesPerPage" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 6px; font-size: 14px; background: white; min-width: 80px;">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100" selected>100</option>
                <option value="200">200</option>
              </select>
            </div>
            <div style="color: #666; font-size: 14px;">
              <span id="custodyTableInfo">جاري التحميل...</span>
            </div>
          </div>
        </div>

        <table id="custodyTable" class="custody-table display nowrap" style="width:100%">
          <thead>
            <tr>
              <th>كود العهد</th>
              <th>اسم العهدة</th>
              <th>النوع</th>
              <th>الحالة</th>
              <th>العدد</th>
              <th>الكمية المصروفة</th>
              <th>الكمية المتبقية</th>
              <th>تاريخ الإضافة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>

    <!-- تسليم العهد -->
    <div class="tab-content" id="deliver-custody" style="display: none;">
      <div class="deliver-custody-section">
        <div class="form-toggle-container">
          <button class="compact-toggle-btn add-btn" id="toggleDeliveryFormBtn">
            <i class="fas fa-plus-circle" id="toggleDeliveryIcon"></i>
            <span id="toggleDeliveryText">تسليم عهدة للموظف</span>
            <i class="fas fa-chevron-down toggle-arrow" id="toggleDeliveryArrow"></i>
          </button>
        </div>
        <div class="delivery-form" id="deliveryFormContainer" style="display: none;">
          <form id="deliverCustodyForm" class="custody-form">
          <!-- إخفاء حقل رقم العملية من خانة الإدخال -->
          <input type="hidden" id="operationNumber">
          <input type="hidden" id="custodyTypeDeliver" readonly>
          <input type="hidden" id="custodyNameDeliver" readonly>
          <input type="hidden" id="deliveryStatus" value="مسلم">

          <!-- الصف الأول: كود الموظف، الإدارة، اسم الموظف، اسم العهدة -->
          <div class="form-row">
            <div class="form-group">
              <label for="employeeCodeDeliver">الكود أو الاسم:</label>
              <input type="text" id="employeeCodeDeliver" required placeholder="أدخل كود أو اسم الموظف">
              <div id="employee-delivery-suggestions" class="suggestions-list" style="display: none;"></div>
            </div>
            <div class="form-group">
              <label for="employeeDepartment">الإدارة:</label>
              <input type="text" id="employeeDepartment" readonly>
            </div>
            <div class="form-group">
              <label for="employeeName">اسم الموظف:</label>
              <input type="text" id="employeeName" readonly>
            </div>
            <div class="form-group">
              <label for="custodyCodeDeliver">إسم العهدة:</label>
              <div class="input-group custody-search-group">
                <input type="text" id="searchCustodyDeliver" placeholder="بحث عن العهدة..." class="search-input custody-search">
                <select id="custodyCodeDeliver" required class="custody-select">
                  <option value="">اختر العهدة</option>
                </select>
              </div>
            </div>
          </div>

          <!-- الصف الثاني: العدد، التاريخ -->
          <div class="form-row">
            <div class="form-group">
              <label for="deliveryQuantity">العدد:</label>
              <input type="number" id="deliveryQuantity" min="1" required>
            </div>
            <div class="form-group">
              <label for="deliveryDate">التاريخ:</label>
              <input type="date" id="deliveryDate" required>
            </div>
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
          </div>

          <!-- أزرار التحكم -->
          <div class="form-row">
            <div class="form-actions" style="grid-column: span 4;">
              <button type="submit" class="save-btn">حفظ التسليم</button>
              <button type="button" class="reset-btn">إعادة تعيين</button>
              <button type="button" id="refreshCustodyDataBtn" class="search-btn">
                <i class="fas fa-sync-alt"></i> تحديث البيانات
              </button>
            </div>
          </div>
        </form>
        </div>
      </div>

      <div class="delivery-table-container">
        <h3>سجل تسليم العهد</h3>

        <!-- فلاتر البحث المتقدمة لتسليم العهد -->
        <div class="form-toggle-container">
          <button class="compact-toggle-btn filter-btn" id="toggleDeliveryFiltersBtn">
            <i class="fas fa-filter" id="toggleDeliveryFiltersIcon"></i>
            <span id="toggleDeliveryFiltersText">فلاتر البحث المتقدمة</span>
            <i class="fas fa-chevron-down toggle-arrow" id="toggleDeliveryFiltersArrow"></i>
          </button>
        </div>
        <div class="advanced-search-container" id="deliveryFiltersContainer" style="display: none;">
          <div class="search-filters-row">
            <div class="form-group">
              <label for="searchDeliveryOperationNumber">رقم العملية:</label>
              <input type="text" id="searchDeliveryOperationNumber" placeholder="ابحث برقم العملية" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchDeliveryEmployeeCode">كود الموظف:</label>
              <input type="text" id="searchDeliveryEmployeeCode" placeholder="ابحث بكود الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchDeliveryEmployeeName">اسم الموظف:</label>
              <input type="text" id="searchDeliveryEmployeeName" placeholder="ابحث باسم الموظف" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchDeliveryCustodyCode">كود العهد:</label>
              <input type="text" id="searchDeliveryCustodyCode" placeholder="ابحث بكود العهد" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchDeliveryStatus">الحالة:</label>
              <select id="searchDeliveryStatus" class="filter-input">
                <option value="">جميع الحالات</option>
                <option value="مسلم">مسلم</option>
                <option value="مرتجع">مرتجع</option>
              </select>
            </div>

            <div class="form-group">
              <label for="searchDeliveryDateFrom">تاريخ التسليم من:</label>
              <input type="date" id="searchDeliveryDateFrom" class="filter-input">
            </div>

            <div class="form-group">
              <label for="searchDeliveryDateTo">تاريخ التسليم إلى:</label>
              <input type="date" id="searchDeliveryDateTo" class="filter-input">
            </div>
          </div>

          <div class="filter-actions">
            <button id="applyAdvancedDeliverySearch" class="search-btn">تطبيق البحث</button>
            <button id="clearAdvancedDeliverySearch" class="reset-btn">مسح البحث</button>
          </div>
        </div>

        <!-- خيارات عرض جدول تسليم العهد -->
        <div class="table-controls" style="margin-bottom: 15px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; align-items: center; gap: 10px;">
              <label for="deliveryEntriesPerPage" style="font-weight: 600; color: #512da8;">عدد الخانات في الصفحة:</label>
              <select id="deliveryEntriesPerPage" style="padding: 8px 12px; border: 2px solid #e0e0e0; border-radius: 6px; font-size: 14px; background: white; min-width: 80px;">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100" selected>100</option>
                <option value="200">200</option>
              </select>
            </div>
            <div style="color: #666; font-size: 14px;">
              <span id="deliveryTableInfo">جاري التحميل...</span>
            </div>
          </div>
        </div>

        <table id="deliveryTable" class="delivery-table display nowrap" style="width:100%">
          <thead>
            <tr>
              <th>رقم العملية</th>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>كود العهد</th>
              <th>اسم العهدة</th>
              <th>نوع العهدة</th>
              <th>العدد</th>
              <th>التاريخ</th>
              <th>الحالة</th>
              <th style="display:none;">المعرف</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>
      </div>
    </div>

    <!-- العهد غير المسلمة -->
    <div class="tab-content" id="undelivered-custody" style="display: none;">
      <div class="undelivered-container">
        <h2>العهد غير المسلمة</h2>
        <div class="undelivered-search" style="margin-bottom: 15px;">
          <input type="text" id="searchUndeliveredAll" placeholder="بحث بالكود أو العهدة أو النوع" style="width: 100%; padding: 10px; font-size: 16px; border-radius: 5px; border: 1px solid #ddd;">
        </div>
        <div class="table-controls" style="margin: 10px 0;">
          <button id="refreshUndeliveredBtn" class="refresh-btn">تحديث</button>
          <button id="exportUndeliveredBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <table class="undelivered-table">
          <thead>
            <tr>
              <th>كود العهد</th>
              <th>اسم العهدة</th>
              <th>النوع</th>
              <th>الحالة</th>
              <th>العدد المتاح</th>
              <th>تاريخ الإضافة</th>
            </tr>
          </thead>
          <tbody id="undeliveredTableBody"></tbody>
        </table>
      </div>
    </div>

    <!-- تقرير عهدة الموظف -->
    <div class="tab-content" id="employee-custody-report" style="display: none;">
      <div class="employee-custody-container">
        <h2>تقرير عهدة الموظف</h2>
        <div class="employee-search" style="margin-bottom: 15px;">
          <div class="form-group" style="flex: 1;">
            <label for="employeeReportSearch">بحث عن موظف:</label>
            <div class="input-group">
              <input type="text" id="employeeReportSearch" placeholder="أدخل كود أو اسم الموظف" style="width: 100%; padding: 10px; font-size: 16px; border-radius: 5px; border: 1px solid #ddd;">
              <button type="button" id="searchEmployeeReportBtn" class="search-btn">بحث</button>
            </div>
          </div>
        </div>
        <div class="table-controls">
          <button id="printEmployeeCustodyBtn" class="print-btn">طباعة</button>
          <button id="exportEmployeeCustodyBtn" class="export-btn">تصدير إلى Excel</button>
        </div>
        <div class="employee-info-container" id="employeeInfoContainer" style="display: none; margin-bottom: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 5px; border: 1px solid #ddd;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <div>
              <strong>كود الموظف:</strong> <span id="reportEmployeeCode"></span>
            </div>
            <div>
              <strong>الاسم الكامل:</strong> <span id="reportEmployeeName"></span>
            </div>
            <div>
              <strong>الإدارة:</strong> <span id="reportEmployeeDepartment"></span>
            </div>
          </div>
        </div>
        <table class="report-table">
          <thead>
            <tr>
              <th>كود العهدة</th>
              <th>اسم العهدة</th>
              <th>نوع العهدة</th>
              <th>تاريخ الاستلام</th>
              <th>الكمية المستلمة</th>
            </tr>
          </thead>
          <tbody id="employeeCustodyTableBody"></tbody>
        </table>
      </div>
    </div>




  </div>



  <!-- Modal for editing custody -->
  <div id="editCustodyModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>تعديل العهدة</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <form id="editCustodyForm">
          <input type="hidden" id="editCustodyId">

          <!-- الصف الأول: اسم العهدة، نوع العهدة، الحالة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editCustodyName">اسم العهدة:</label>
              <input type="text" id="editCustodyName" required>
            </div>

            <div class="form-group">
              <label for="editCustodyType">نوع العهدة:</label>
              <select id="editCustodyType" required>
                <option value="لابتوب">لابتوب</option>
                <option value="موبايل">موبايل</option>
                <option value="شاشة">شاشة</option>
                <option value="طابعة">طابعة</option>
                <option value="كيبورد">كيبورد</option>
                <option value="ماوس">ماوس</option>
                <option value="أخرى">أخرى</option>
              </select>
            </div>

            <div class="form-group">
              <label for="editCustodyStatus">الحالة:</label>
              <select id="editCustodyStatus" required>
                  <option value="جديدة">جديدة</option>
                  <option value="مستعمل">مستعمل</option>
                  <option value="صيانة">صيانة</option>
                </select>
            </div>
          </div>

          <!-- الصف الثاني: العدد -->
          <div class="form-row">
            <div class="form-group">
              <label for="editCustodyQuantity">العدد:</label>
              <input type="number" id="editCustodyQuantity" min="1" required>
            </div>
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
            <div class="form-group"></div> <!-- حقل فارغ للتوازن -->
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button id="saveEditBtn" class="confirm-btn">حفظ التغييرات</button>
        <button class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>

  <!-- Modal for editing delivery record -->
  <div id="editDeliveryModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>تعديل سجل تسليم العهد</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <form id="editDeliveryForm">
          <input type="hidden" id="editDeliveryId">
          <!-- الصف الأول: رقم العملية، كود الموظف، اسم الموظف -->
          <div class="form-row">
            <div class="form-group">
              <label for="editDeliveryOperationNumber">رقم العملية:</label>
              <input type="text" id="editDeliveryOperationNumber" readonly>
            </div>
            <div class="form-group">
              <label for="editDeliveryEmployeeCode">كود الموظف:</label>
              <input type="text" id="editDeliveryEmployeeCode" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
              <label for="editDeliveryEmployeeName">اسم الموظف:</label>
              <input type="text" id="editDeliveryEmployeeName" readonly style="background-color: #f5f5f5;">
            </div>
          </div>

          <!-- الصف الثاني: القسم، كود العهدة، اسم العهدة -->
          <div class="form-row">
            <div class="form-group">
              <label for="editDeliveryDepartment">القسم:</label>
              <input type="text" id="editDeliveryDepartment" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
              <label for="editDeliveryCustodyCode">كود العهدة:</label>
              <input type="text" id="editDeliveryCustodyCode" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
              <label for="editDeliveryCustodyName">اسم العهدة:</label>
              <input type="text" id="editDeliveryCustodyName" readonly style="background-color: #f5f5f5;">
            </div>
          </div>

          <!-- الصف الثالث: نوع العهدة، الكمية، تاريخ التسليم -->
          <div class="form-row">
            <div class="form-group">
              <label for="editDeliveryCustodyType">نوع العهدة:</label>
              <input type="text" id="editDeliveryCustodyType" readonly style="background-color: #f5f5f5;">
            </div>
            <div class="form-group">
              <label for="editDeliveryQuantity">الكمية:</label>
              <input type="number" id="editDeliveryQuantity" min="1" required>
            </div>
            <div class="form-group">
              <label for="editDeliveryDate">تاريخ التسليم:</label>
              <input type="date" id="editDeliveryDate" required>
            </div>
          </div>

          <!-- الصف الرابع: الحالة (مخفي - افتراضي مسلم) -->
          <input type="hidden" id="editDeliveryStatus" value="مسلم">
        </form>
      </div>
      <div class="modal-footer">
        <button id="saveEditDeliveryBtn" class="confirm-btn">حفظ التغييرات</button>
        <button class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>



  <script src="shared-utils.js"></script>
  <script src="dateUtils.js"></script>
  <script src="form-validation.js"></script>

  <!-- jQuery (مطلوب لـ DataTables) -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

  <!-- DataTables JavaScript -->
  <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
  <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

  <!-- مكتبة SheetJS لتصدير Excel مع دعم RTL -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

  <!-- مكتبة مساعدة لدعم RTL في Excel -->
  <script src="excel-rtl-utils.js"></script>
  <script src="custody.js"></script>

  

<script>
// دالة تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح البيانات المحفوظة
        localStorage.removeItem('token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('activeSection');
        localStorage.removeItem('userName');
        
        // إعادة التوجيه لصفحة تسجيل الدخول
        window.location.href = 'login.html';
    }
}

// تم حذف دالة updateSidebarUserInfo المتعارضة - يتم التحكم بها من sidebar-standalone.js فقط

// تحميل القائمة الجانبية مع النظام المحسن
</script>
</body>
</html>