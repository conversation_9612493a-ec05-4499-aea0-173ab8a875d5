const express = require("express");
const { pool } = require("../config/database");
const { authenticateToken, checkPermission } = require("../middleware/auth");
const { logAction, createEditMessage, hasActualChanges } = require('../activityLogger');
const { enhanceSearchQuery } = require('../utils/textUtils');
const { addDepartmentFilter, addDepartmentFilterToQuery } = require('../middleware/departmentFilter');

const router = express.Router();

// الحصول على الكود التلقائي التالي
router.get('/next-code', authenticateToken, async (req, res) => {
  try {
    const [result] = await pool.promise().query(
      "SELECT MAX(code) as max_code FROM employees"
    );

    const nextCode = result[0].max_code ? result[0].max_code + 1 : 1;

    res.json({
      next_code: nextCode,
      nextCode: nextCode  // دعم كلا التنسيقين
    });
  } catch (error) {
    console.error('خطأ في الحصول على الكود التلقائي:', error);
    res.status(500).json({ error: 'فشل في الحصول على الكود التلقائي' });
  }
});

// الحصول على جميع الموظفين أو البحث بالاسم/الكود/القسم/الوظيفة
router.get('/', authenticateToken, addDepartmentFilter, async (req, res) => {
  try {
    const { search, include_resigned, department, job_title } = req.query;
    
    // تسجيل معاملات البحث للتشخيص
    console.log('معاملات البحث المستلمة:', { search, include_resigned, department, job_title });
    
    let query = "SELECT * FROM employees";
    let params = [];
    let whereConditions = [];

    // إضافة شرط الحالة (نشط فقط إلا إذا طُلب تضمين المستقيلين)
    if (include_resigned !== 'true') {
      whereConditions.push("status = 'نشط'");
    }

    // إضافة شرط القسم إذا تم توفيره
    if (department && department !== '') {
      whereConditions.push("department = ?");
      params.push(department);
    }

    // إضافة شرط الوظيفة إذا تم توفيرها
    if (job_title && job_title !== '') {
      whereConditions.push("job_title = ?");
      params.push(job_title);
    }

    // إضافة فلترة الإدارات
    if (req.allowedDepartments === null) {
      // null يعني المستخدم admin - لا نطبق فلترة
      // لا نضيف أي شروط إضافية
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      whereConditions.push(`department IN (${departmentPlaceholders})`);
      params.push(...req.allowedDepartments);
    } else {
      // إذا لم يكن لديه صلاحية لأي إدارة، لا يعرض أي موظفين
      whereConditions.push("1 = 0");
    }

    // إضافة شروط WHERE إذا وجدت
    if (whereConditions.length > 0) {
      query += " WHERE " + whereConditions.join(" AND ");
    }

    // إضافة البحث النصي إذا تم توفيره
    if (search && search !== '') {
      const searchColumns = ['full_name', 'code'];
      
      // تعديل الاستعلام بناءً على وجود شروط سابقة
      if (whereConditions.length > 0) {
        // إذا كانت هناك شروط سابقة، نستخدم AND
        query += " AND (";
        const searchConditions = searchColumns.map(column => `${column} LIKE ?`);
        query += searchConditions.join(" OR ");
        query += ")";
        
        // إضافة معاملات البحث
        searchColumns.forEach(() => {
          params.push(`%${search}%`);
        });
      } else {
        // إذا لم تكن هناك شروط سابقة، نبدأ بـ WHERE
        query += " WHERE (";
        const searchConditions = searchColumns.map(column => `${column} LIKE ?`);
        query += searchConditions.join(" OR ");
        query += ")";
        
        // إضافة معاملات البحث
        searchColumns.forEach(() => {
          params.push(`%${search}%`);
        });
      }
    }

    query += " ORDER BY code";
    
    // تسجيل الاستعلام النهائي للتشخيص
    console.log('الاستعلام النهائي:', query);
    console.log('المعاملات:', params);

    const [rows] = await pool.promise().query(query, params);
    console.log(`تم العثور على ${rows.length} موظف`);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب الموظفين:', error);
    res.status(500).json({ error: 'فشل في جلب الموظفين' });
  }
});

// البحث عن الموظفين
router.post('/search', authenticateToken, addDepartmentFilter, checkPermission('view_employees'), async (req, res) => {
  try {
    const { searchTerm, include_resigned } = req.body;

    if (!searchTerm) {
      return res.status(400).json({ error: 'يرجى توفير مصطلح البحث' });
    }

    let baseQuery = "SELECT * FROM employees";
    let params = [];

    // إضافة شرط الحالة (نشط فقط إلا إذا طُلب تضمين المستقيلين)
    if (include_resigned !== true) {
      baseQuery += " WHERE status = 'نشط'";
    }

    // إضافة البحث المحسن
    const searchColumns = ['full_name', 'code'];
    const enhanced = enhanceSearchQuery(baseQuery, searchTerm, searchColumns, params);
    let query = enhanced.query;
    params = enhanced.params;

    query += " ORDER BY code";

    const [rows] = await pool.promise().query(query, params);

    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث عن الموظفين:', error);
    res.status(500).json({ error: 'فشل في البحث عن الموظفين' });
  }
});

// الحصول على موظف محدد بالكود
router.get('/:code', authenticateToken, checkPermission('view_employees'), async (req, res) => {
  try {
    const { code } = req.params;

    const [rows] = await pool.promise().query(
      "SELECT * FROM employees WHERE code = ?",
      [code]
    );

    if (rows.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    res.json(rows[0]);
  } catch (error) {
    console.error('خطأ في جلب الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب الموظف' });
  }
});



// إضافة موظف جديد
router.post('/', authenticateToken, async (req, res) => {
  try {
    // تسجيل البيانات المستلمة للتشخيص
    console.log('📝 بيانات الموظف المستلمة:', req.body);

    const {
      employee_code,
      code,  // دعم الحقل القديم
      name,
      full_name,
      department,
      position,
      job_title,
      hire_date,
      phone,
      address,
      qualification,
      national_id,
      birth_date,
      marital_status,
      children,
      social_insurance,
      insurance_number,
      insurance_entity,
      insurance_start,
      insurance_job,
      insurance_salary,
      worker_cost,
      company_cost,
      total_salary,
      health_card,
      skill_level,
      skill_start,
      skill_end,
      skill_remaining,
      skill_job,
      leave_balance,
      leave_used,
      leave_remaining,
      special_needs,
      contract_start_date,
      contract_end_date,
      status
    } = req.body;

    // دعم كلا من التنسيقين القديم والجديد
    const finalEmployeeCode = employee_code || code;
    const finalName = full_name || name;
    const finalJobTitle = job_title || position;

    console.log('🔍 البيانات المعالجة:', {
      finalEmployeeCode,
      finalName,
      finalJobTitle,
      department
    });

    if (!finalEmployeeCode || !finalName) {
      console.log('❌ بيانات مفقودة:', { finalEmployeeCode, finalName });
      return res.status(400).json({
        error: 'كود الموظف والاسم مطلوبان',
        received: { employee_code, code, name, full_name }
      });
    }

    // التحقق من عدم وجود موظف بنفس الكود
    const [existingEmployee] = await pool.promise().query(
      "SELECT code FROM employees WHERE code = ?",
      [finalEmployeeCode]
    );

    if (existingEmployee.length > 0) {
      return res.status(400).json({ error: 'كود الموظف موجود بالفعل' });
    }

    // إنشاء query ديناميكي بناءً على الحقول المتاحة
    let insertFields = ['code', 'full_name'];
    let insertValues = [finalEmployeeCode, finalName];
    let placeholders = ['?', '?'];

    // إضافة الحقول الاختيارية إذا كانت متوفرة
    const optionalFields = {
      department: department,
      job_title: finalJobTitle,
      hire_date: hire_date,
      address: address,
      qualification: qualification,
      phone: phone,
      birth_date: birth_date,
      marital_status: marital_status,
      children: children,
      national_id: national_id,
      social_insurance: social_insurance,
      insurance_number: insurance_number,
      insurance_entity: insurance_entity,
      insurance_start: insurance_start,
      insurance_job: insurance_job,
      insurance_salary: insurance_salary,
      worker_cost: worker_cost,
      company_cost: company_cost,
      total_salary: total_salary,
      health_card: health_card,
      skill_level: skill_level,
      skill_start: skill_start,
      skill_end: skill_end,
      skill_remaining: skill_remaining,
      skill_job: skill_job,
      leave_balance: leave_balance || 0,
      leave_used: leave_used,
      leave_remaining: leave_remaining,
      special_needs: special_needs,
      contract_start_date: contract_start_date,
      contract_end_date: contract_end_date,
      status: status || 'نشط'
    };

    // إضافة الحقول التي لها قيم
    Object.keys(optionalFields).forEach(field => {
      if (optionalFields[field] !== undefined && optionalFields[field] !== null && optionalFields[field] !== '') {
        insertFields.push(field);
        insertValues.push(optionalFields[field]);
        placeholders.push('?');
      }
    });

    const [result] = await pool.promise().query(
      `INSERT INTO employees (${insertFields.join(', ')}) VALUES (${placeholders.join(', ')})`,
      insertValues
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'employees',
      record_id: result.insertId.toString(),
      message: `تم إضافة موظف جديد: ${finalName} (كود: ${finalEmployeeCode}) - القسم: ${department || 'غير محدد'} - المنصب: ${finalJobTitle || 'غير محدد'}`
    });

    res.status(201).json({
      id: result.insertId,
      code: finalEmployeeCode,
      employee_code: finalEmployeeCode,  // دعم كلا التنسيقين
      name: finalName,
      message: 'تم إضافة الموظف بنجاح'
    });
  } catch (error) {
    console.error('خطأ في إضافة الموظف:', error);
    res.status(500).json({ error: 'فشل في إضافة الموظف' });
  }
});

// تحديث موظف
router.put('/:code', authenticateToken, async (req, res) => {
  try {
    const { code } = req.params;
    const updateData = req.body;

    // التحقق من وجود الموظف والحصول على البيانات القديمة
    const [existingEmployee] = await pool.promise().query(
      "SELECT * FROM employees WHERE code = ?",
      [code]
    );

    if (existingEmployee.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    const oldData = existingEmployee[0];

    // إزالة الحقول غير المسموح بتحديثها
    delete updateData.id;

    // فلترة الحقول المسموحة فقط حسب الجدول الفعلي
    const allowedFields = [
      'code', 'full_name', 'department', 'job_title', 'hire_date', 'contract_start_date', 'contract_end_date', 'address',
      'qualification', 'phone', 'birth_date', 'marital_status', 'children',
      'national_id', 'social_insurance', 'insurance_number', 'insurance_entity',
      'insurance_start', 'insurance_job', 'insurance_salary', 'worker_cost',
      'company_cost', 'total_salary', 'health_card', 'skill_level', 'skill_start',
      'skill_end', 'skill_remaining', 'skill_job', 'leave_balance', 'leave_used',
      'leave_remaining', 'special_needs', 'photo', 'documents', 'status'
    ];

    // إزالة أي حقول غير مسموحة
    Object.keys(updateData).forEach(key => {
      if (!allowedFields.includes(key)) {
        delete updateData[key];
      }
    });

    // إذا تم تغيير كود الموظف، التحقق من عدم وجود موظف آخر بنفس الكود
    if (updateData.code && updateData.code !== code) {
      const [duplicateEmployee] = await pool.promise().query(
        "SELECT code FROM employees WHERE code = ?",
        [updateData.code]
      );

      if (duplicateEmployee.length > 0) {
        return res.status(400).json({ error: 'كود الموظف الجديد موجود بالفعل' });
      }
    }

    // قائمة حقول التاريخ التي تحتاج معالجة خاصة (حسب الجدول الفعلي)
    const dateFields = [
      'birth_date', 'hire_date', 'insurance_start', 'skill_start', 'skill_end', 'contract_start_date', 'contract_end_date'
    ];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    dateFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        if (updateData[field] === '' || updateData[field] === null || updateData[field] === undefined) {
          updateData[field] = null;
        }
      }
    });

    // التحقق من وجود تغييرات فعلية
    if (!hasActualChanges(oldData, updateData)) {
      return res.status(400).json({ error: 'لا يوجد تغيير في البيانات' });
    }

    // تحضير البيانات للتحديث
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);

    if (fields.length === 0) {
      return res.status(400).json({ error: 'لم يتم توفير أي بيانات للتحديث' });
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    values.push(code);

    await pool.promise().query(
      `UPDATE employees SET ${setClause} WHERE code = ?`,
      values
    );

    // تسجيل النشاط
    const newData = { ...oldData, ...updateData };

    const fieldLabels = {
      code: 'كود الموظف',
      full_name: 'الاسم الكامل',
      department: 'القسم',
      job_title: 'المنصب',
      hire_date: 'تاريخ التوظيف',
      address: 'العنوان',
      qualification: 'المؤهل',
      phone: 'رقم الهاتف',
      birth_date: 'تاريخ الميلاد',
      marital_status: 'الحالة الاجتماعية',
      children: 'عدد الأطفال',
      national_id: 'الرقم القومي',
      social_insurance: 'التأمين الاجتماعي',
      insurance_number: 'رقم التأمين',
      insurance_entity: 'جهة التأمين',
      insurance_start: 'تاريخ بداية التأمين',
      insurance_job: 'وظيفة التأمين',
      insurance_salary: 'راتب التأمين',
      worker_cost: 'تكلفة العامل',
      company_cost: 'تكلفة الشركة',
      total_salary: 'إجمالي الراتب',
      health_card: 'البطاقة الصحية',
      skill_level: 'مستوى المهارة',
      skill_start: 'تاريخ بداية المهارة',
      skill_end: 'تاريخ انتهاء المهارة',
      skill_remaining: 'المهارة المتبقية',
      skill_job: 'وظيفة المهارة',
      leave_balance: 'رصيد الإجازات',
      leave_used: 'الإجازات المستخدمة',
      leave_remaining: 'الإجازات المتبقية',
      special_needs: 'الاحتياجات الخاصة',
      contract_start_date: 'تاريخ بداية العقد',
      contract_end_date: 'تاريخ انتهاء العقد',
      status: 'الحالة'
    };

    const editMessage = createEditMessage(
      `موظف: ${newData.full_name || newData.code}`,
      oldData,
      newData,
      fieldLabels
    );

    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'employees',
      record_id: code.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث الموظف بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث الموظف:', error);
    res.status(500).json({ error: 'فشل في تحديث الموظف' });
  }
});

// حذف موظف محدد
router.delete('/:code', authenticateToken, async (req, res) => {
  try {
    const { code } = req.params;

    // الحصول على بيانات الموظف قبل الحذف
    const [employeeData] = await pool.promise().query(
      "SELECT * FROM employees WHERE code = ?",
      [code]
    );

    if (employeeData.length === 0) {
      return res.status(404).json({ error: 'الموظف غير موجود' });
    }

    const employee = employeeData[0];

    // حماية سجل تسليم العهد من الحذف
    // إزالة القيد الخارجي مؤقتاً لحماية سجلات التسليم
    try {
      // تعطيل فحص القيود الخارجية مؤقتاً
      await pool.promise().query("SET FOREIGN_KEY_CHECKS = 0");

      // حذف الموظف بدون تأثير على سجل تسليم العهد
      const [result] = await pool.promise().query(
        "DELETE FROM employees WHERE code = ?",
        [code]
      );

      // إعادة تفعيل فحص القيود الخارجية
      await pool.promise().query("SET FOREIGN_KEY_CHECKS = 1");

      console.log(`تم حذف الموظف ${employee.full_name} (${code}) مع الحفاظ على سجل تسليم العهد`);
    } catch (deleteError) {
      // إعادة تفعيل فحص القيود الخارجية في حالة الخطأ
      await pool.promise().query("SET FOREIGN_KEY_CHECKS = 1");
      throw deleteError;
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'employees',
      record_id: code.toString(),
      message: `تم حذف موظف: ${employee.full_name} (كود: ${employee.code}) - القسم: ${employee.department || 'غير محدد'} - المنصب: ${employee.job_title || 'غير محدد'}`
    });

    res.json({ message: 'تم حذف الموظف بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الموظف:', error);
    res.status(500).json({ error: 'فشل في حذف الموظف' });
  }
});

// حذف جميع الموظفين
router.delete('/', authenticateToken, async (req, res) => {
  try {
    // حماية سجل تسليم العهد من الحذف
    // تعطيل فحص القيود الخارجية مؤقتاً
    await pool.promise().query("SET FOREIGN_KEY_CHECKS = 0");

    // حذف جميع الموظفين بدون تأثير على سجل تسليم العهد
    await pool.promise().query("DELETE FROM employees");

    // إعادة تفعيل فحص القيود الخارجية
    await pool.promise().query("SET FOREIGN_KEY_CHECKS = 1");

    console.log('تم حذف جميع الموظفين مع الحفاظ على سجل تسليم العهد');
    res.json({ message: 'تم حذف جميع الموظفين بنجاح مع الحفاظ على سجل تسليم العهد' });
  } catch (error) {
    // إعادة تفعيل فحص القيود الخارجية في حالة الخطأ
    await pool.promise().query("SET FOREIGN_KEY_CHECKS = 1");
    console.error('خطأ في حذف جميع الموظفين:', error);
    res.status(500).json({ error: 'فشل في حذف جميع الموظفين' });
  }
});

// حساب رصيد الإجازات
router.post('/calculate-leave-balance', authenticateToken, checkPermission('can_edit'), async (req, res) => {
  try {
    const [employees] = await pool.promise().query("SELECT * FROM employees");
    
    for (const employee of employees) {
      let annualLeave = 21; // الإجازة السنوية الافتراضية
      
      if (employee.hire_date) {
        const hireDate = new Date(employee.hire_date);
        const currentDate = new Date();
        
        // التحقق من صحة تاريخ التوظيف
        if (!isNaN(hireDate.getTime()) && hireDate <= currentDate) {
          const yearsOfService = Math.floor((currentDate - hireDate) / (365.25 * 24 * 60 * 60 * 1000));
          
          // حساب الإجازة السنوية بناءً على سنوات الخدمة
          if (yearsOfService >= 10) {
            annualLeave = 30;
          } else if (yearsOfService >= 5) {
            annualLeave = 25;
          } else {
            annualLeave = 21;
          }
        } else {
          console.log(`تاريخ توظيف غير صحيح للموظف ${employee.full_name}: ${employee.hire_date}`);
        }
      } else {
        console.log(`لا يوجد تاريخ توظيف للموظف ${employee.full_name}`);
      }
      
      // تحديث رصيد الإجازات
      await pool.promise().query(
        "UPDATE employees SET leave_balance = ? WHERE code = ?",
        [annualLeave, employee.code]
      );
    }
    
    res.json({ message: 'تم حساب وتحديث رصيد الإجازات لجميع الموظفين بنجاح' });
  } catch (error) {
    console.error('خطأ في حساب رصيد الإجازات:', error);
    res.status(500).json({ error: 'فشل في حساب رصيد الإجازات' });
  }
});

// الحصول على الأقسام
router.get('/departments', authenticateToken, checkPermission('view_employees'), async (req, res) => {
  try {
    // استخدام جدول departments بدلاً من استخراج الأقسام من جدول employees
    const [rows] = await pool.promise().query(
      "SELECT name FROM departments WHERE is_active = 1 ORDER BY name"
    );

    const departments = rows.map(row => row.name);
    res.json(departments);
  } catch (error) {
    console.error('خطأ في جلب الأقسام:', error);
    res.status(500).json({ error: 'فشل في جلب الأقسام' });
  }
});

// الحصول على الوظائف
router.get('/job-titles', authenticateToken, checkPermission('view_employees'), async (req, res) => {
  try {
    const [rows] = await pool.promise().query(
      "SELECT DISTINCT job_title FROM employees WHERE job_title IS NOT NULL AND job_title != '' ORDER BY job_title"
    );
    
    const jobTitles = rows.map(row => row.job_title);
    res.json(jobTitles);
  } catch (error) {
    console.error('خطأ في جلب الوظائف:', error);
    res.status(500).json({ error: 'فشل في جلب الوظائف' });
  }
});

// اختبار بسيط لإضافة موظف
router.post('/test-add', authenticateToken, async (req, res) => {
  try {
    const { employee_code, name } = req.body;

    if (!employee_code || !name) {
      return res.status(400).json({ error: 'كود الموظف والاسم مطلوبان' });
    }

    const [result] = await pool.promise().query(
      "INSERT INTO employees (code, full_name, status) VALUES (?, ?, ?)",
      [employee_code, name, 'نشط']
    );

    res.status(201).json({
      id: result.insertId,
      employee_code,
      name,
      message: 'تم إضافة الموظف بنجاح (اختبار بسيط)'
    });
  } catch (error) {
    console.error('خطأ في إضافة الموظف (اختبار):', error);
    res.status(500).json({ error: 'فشل في إضافة الموظف', details: error.message });
  }
});

module.exports = router;