// إدارة الإدارات - JavaScript

let departmentsTable;
let currentUser = null;
const API_URL = localStorage.getItem('serverUrl') || 'http://localhost:5500/api';

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    await checkAuthentication();
    await initializePage();
    setupEventListeners();
});

// التحقق من المصادقة والصلاحيات
async function checkAuthentication() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = 'login.html';
        return;
    }

    try {
        const response = await fetch(`${API_URL}/verify`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('فشل في التحقق من التوكن');
        }

        const data = await response.json();
        currentUser = data.user;

        // التحقق من الصلاحية
        if (!currentUser.permissions.manage_department_permissions && currentUser.username !== 'admin') {
            showMessage('ليس لديك صلاحية لإدارة الإدارات', 'error');
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 2000);
            return;
        }

    } catch (error) {
        console.error('خطأ في التحقق من المصادقة:', error);
        localStorage.removeItem('token');
        window.location.href = 'login.html';
    }
}

// تهيئة الصفحة
async function initializePage() {
    await loadDepartments();
    initializeDataTable();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // أزرار الهيدر
    document.getElementById('addDepartmentBtn').addEventListener('click', () => openAddModal());
    document.getElementById('backBtn').addEventListener('click', () => window.location.href = 'dashboard.html');
    document.getElementById('logoutBtn').addEventListener('click', logout);
    document.getElementById('refreshBtn').addEventListener('click', refreshDepartments);

    // Modal events
    document.getElementById('closeModal').addEventListener('click', closeModal);
    document.getElementById('cancelBtn').addEventListener('click', closeModal);
    document.getElementById('saveDepartmentBtn').addEventListener('click', saveDepartment);

    // Delete modal events
    document.getElementById('closeDeleteModal').addEventListener('click', closeDeleteModal);
    document.getElementById('cancelDeleteBtn').addEventListener('click', closeDeleteModal);
    document.getElementById('confirmDeleteBtn').addEventListener('click', confirmDelete);

    // إغلاق Modal عند النقر خارجه
    window.addEventListener('click', function(event) {
        const departmentModal = document.getElementById('departmentModal');
        const deleteModal = document.getElementById('deleteModal');
        
        if (event.target === departmentModal) {
            closeModal();
        }
        if (event.target === deleteModal) {
            closeDeleteModal();
        }
    });

    // إغلاق Modal بمفتاح Escape
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeModal();
            closeDeleteModal();
        }
    });
}

// تحميل الإدارات
async function loadDepartments() {
    try {
        showLoading(true);
        const token = localStorage.getItem('token');
        
        const response = await fetch(`${API_URL}/department-permissions/departments`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('فشل في تحميل الإدارات');
        }

        const data = await response.json();
        const departments = data.data || [];
        
        displayDepartments(departments);
        
    } catch (error) {
        console.error('خطأ في تحميل الإدارات:', error);
        showMessage('حدث خطأ في تحميل الإدارات', 'error');
    } finally {
        showLoading(false);
    }
}

// عرض الإدارات في الجدول
function displayDepartments(departments) {
    const tbody = document.getElementById('departmentsTableBody');
    tbody.innerHTML = '';

    departments.forEach((dept, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td><strong>${dept.name}</strong></td>
            <td>${dept.description || 'لا يوجد وصف'}</td>
            <td>${formatDate(dept.created_at)}</td>
            <td>
                <span class="status-badge ${dept.is_active ? 'status-active' : 'status-inactive'}">
                    ${dept.is_active ? 'نشطة' : 'غير نشطة'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" onclick="openEditModal(${dept.id}, '${dept.name}', '${dept.description || ''}')">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="action-btn delete-btn" onclick="openDeleteModal(${dept.id}, '${dept.name}')">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });

    // تحديث DataTable إذا كان موجوداً
    if (departmentsTable) {
        departmentsTable.destroy();
    }
    initializeDataTable();
}

// تهيئة DataTable
function initializeDataTable() {
    departmentsTable = $('#departmentsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 10,
        order: [[1, 'asc']], // ترتيب حسب اسم الإدارة
        columnDefs: [
            { orderable: false, targets: [5] } // عمود الإجراءات غير قابل للترتيب
        ]
    });
}

// فتح modal الإضافة
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة إدارة جديدة';
    document.getElementById('departmentId').value = '';
    document.getElementById('departmentName').value = '';
    document.getElementById('departmentDescription').value = '';
    document.getElementById('departmentModal').style.display = 'flex';
    document.getElementById('departmentName').focus();
}

// فتح modal التعديل
function openEditModal(id, name, description) {
    document.getElementById('modalTitle').textContent = 'تعديل الإدارة';
    document.getElementById('departmentId').value = id;
    document.getElementById('departmentName').value = name;
    document.getElementById('departmentDescription').value = description;
    document.getElementById('departmentModal').style.display = 'flex';
    document.getElementById('departmentName').focus();
}

// إغلاق modal
function closeModal() {
    document.getElementById('departmentModal').style.display = 'none';
    document.getElementById('departmentForm').reset();
}

// فتح modal الحذف
function openDeleteModal(id, name) {
    document.getElementById('departmentToDelete').textContent = name;
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', id);
    document.getElementById('deleteModal').style.display = 'flex';
}

// إغلاق modal الحذف
function closeDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

// حفظ الإدارة (إضافة أو تعديل)
async function saveDepartment() {
    const id = document.getElementById('departmentId').value;
    const name = document.getElementById('departmentName').value.trim();
    const description = document.getElementById('departmentDescription').value.trim();

    if (!name) {
        showMessage('يرجى إدخال اسم الإدارة', 'error');
        return;
    }

    try {
        showLoading(true);
        const token = localStorage.getItem('token');
        
        const url = id ? 
            `${API_URL}/department-permissions/departments/${id}` : 
            `${API_URL}/department-permissions/departments`;
        
        const method = id ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                name: name,
                description: description
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || 'فشل في حفظ الإدارة');
        }

        showMessage(data.message, 'success');
        closeModal();
        await refreshDepartments();

    } catch (error) {
        console.error('خطأ في حفظ الإدارة:', error);
        showMessage(error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// تأكيد الحذف
async function confirmDelete() {
    const id = document.getElementById('confirmDeleteBtn').getAttribute('data-id');
    
    try {
        showLoading(true);
        const token = localStorage.getItem('token');
        
        const response = await fetch(`${API_URL}/department-permissions/departments/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || 'فشل في حذف الإدارة');
        }

        showMessage(data.message, 'success');
        closeDeleteModal();
        await refreshDepartments();

    } catch (error) {
        console.error('خطأ في حذف الإدارة:', error);
        showMessage(error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// تحديث قائمة الإدارات
async function refreshDepartments() {
    await loadDepartments();
}

// عرض مؤشر التحميل
function showLoading(show) {
    const loading = document.getElementById('loading');
    loading.style.display = show ? 'block' : 'none';
}

// عرض الرسائل
function showMessage(message, type = 'info') {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    container.appendChild(messageDiv);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('token');
        window.location.href = 'login.html';
    }
}
