<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
  <meta charset="UTF-8">
  <title>تعديل بيانات الموظف</title>
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="edit.css">
  <style>
    .form-text.text-muted {
      font-size: 0.875rem;
      color: #6c757d;
      margin-top: 0.25rem;
      display: block;
    }
  </style>
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="permissions.js" defer></script>
</head>

<body class="edit-page">

  <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="dashboard.html" class="dashboard-btn">
      <i class="fas fa-home"></i>
      <span>العودة للوحة التحكم</span>
    </a>
  </div>

  <!-- المحتوى الرئيسي -->
  <div class="main-content full-width" id="mainContent">
    <h1 class="form-title">تعديل بيانات الموظف</h1>
    <form id="editEmployeeForm" class="modern-form">
      <div class="form-grid">
        <div class="form-group">
          <label>كود الموظف</label>
          <input type="text" name="code" required readonly />
        </div>
        <div class="form-group">
          <label>الاسم الكامل</label>
          <input type="text" name="full_name" required />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label><i class="fas fa-building"></i> الإدارة</label>
          <select name="department" id="departmentSelect">
            <option value="">اختر الإدارة</option>
          </select>
        </div>
        <div class="form-group">
          <label>الوظيفة</label>
          <input type="text" name="job_title" />
        </div>
        <div class="form-group">
          <label>تاريخ التعيين <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="hire_date" />
        </div>
        <div class="form-group">
          <label>تاريخ بداية عقد العمل <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="contract_start_date" />
        </div>
        <div class="form-group">
          <label>تاريخ انتهاء عقد العمل <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="contract_end_date" id="contract_end_date" />
        </div>
        <div class="form-group">
          <label>المدة المتبقية من العقد <i class="fa-regular fa-clock"></i></label>
          <input type="text" id="contract_remaining" readonly placeholder="سيتم الحساب تلقائياً"
            style="background-color: #f8f9fa; color: #6c757d; cursor: not-allowed;" />
          <small class="form-text text-muted">حقل استرشادي - يحسب المدة المتبقية من اليوم حتى انتهاء العقد</small>
        </div>
        <div class="form-group">
          <label>العنوان</label>
          <input type="text" name="address" />
        </div>
        <div class="form-group">
          <label>المؤهل</label>
          <input type="text" name="qualification" />
        </div>
        <div class="form-group">
          <label>التليفون</label>
          <input type="text" name="phone" />
        </div>
        <div class="form-group">
          <label>تاريخ الميلاد <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="birth_date" />
        </div>
        <div class="form-group">
          <label>الحالة الاجتماعية</label>
          <input type="text" name="marital_status" />
        </div>
        <div class="form-group">
          <label>عدد الأبناء</label>
          <input type="text" name="children" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>الرقم القومي</label>
          <input type="text" name="national_id" />
        </div>
        <div class="form-group">
          <label>التأمين التكافلي</label>
          <input type="text" name="social_insurance" />
        </div>
        <div class="form-group">
          <label>الرقم التأميني</label>
          <input type="text" name="insurance_number" />
        </div>
        <div class="form-group">
          <label>جهة التأمين</label>
          <input type="text" name="insurance_entity" />
        </div>
        <div class="form-group">
          <label>تاريخ التأمين عليه <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="insurance_start" />
        </div>
        <div class="form-group">
          <label>المهنة في التأمينات</label>
          <input type="text" name="insurance_job" />
        </div>
        <div class="form-group">
          <label>راتب التأمينات</label>
          <input type="text" name="insurance_salary" />
        </div>
        <div class="form-group">
          <label>ما يتحمله العامل</label>
          <input type="text" name="worker_cost" />
        </div>
        <div class="form-group">
          <label>ما تتحمله الشركة</label>
          <input type="text" name="company_cost" />
        </div>
        <div class="form-group">
          <label>الأجر الشامل</label>
          <input type="text" name="total_salary" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>رقم البطاقة الصحية</label>
          <input type="text" name="health_card" />
        </div>
        <div class="form-group">
          <label>قياس المهارة</label>
          <input type="text" name="skill_level" />
        </div>
        <div class="form-group">
          <label>تاريخ بداية قياس المهارة <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="skill_start" />
        </div>
        <div class="form-group">
          <label>تاريخ انتهاء قياس المهارة <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="skill_end" />
        </div>
        <div class="form-group">
          <label>الوقت المتبقى على انتهاء قياس المهارة</label>
          <input type="text" name="skill_remaining" />
        </div>
        <div class="form-group">
          <label>مهنة قياس المهارة</label>
          <input type="text" name="skill_job" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>رصيد الإجازات</label>
          <input type="text" name="leave_balance" readonly style="background-color: #f5f5f5; cursor: not-allowed;"
            title="هذا الحقل محسوب تلقائياً ولا يمكن تعديله" />
        </div>
        <div class="form-group">
          <label>الإجازات المستخدمة</label>
          <input type="text" name="leave_used" readonly style="background-color: #f5f5f5; cursor: not-allowed;"
            title="هذا الحقل محسوب تلقائياً ولا يمكن تعديله" />
        </div>
        <div class="form-group">
          <label>الإجازات المتبقية</label>
          <input type="text" name="leave_remaining" readonly style="background-color: #f5f5f5; cursor: not-allowed;"
            title="هذا الحقل محسوب تلقائياً ولا يمكن تعديله" />
        </div>
        <div class="form-group">
          <label>ذوي الهمم</label>
          <input type="text" name="special_needs" />
        </div>

      </div>
      <div class="form-actions">
        <button type="submit" class="save-btn">حفظ التعديلات</button>
        <button type="button" id="refreshBtn" class="refresh-btn"><i class="fas fa-sync-alt"></i> تحديث
          البيانات</button>
        <button type="button" onclick="cancelEdit()" class="cancel-btn">إلغاء</button>
      </div>
    </form>
  </div>

  <!-- نافذة منبثقة لإضافة إدارة جديدة -->
  <div id="departmentModal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2>إضافة إدارة جديدة</h2>
        <span class="close" onclick="closeDepartmentModal()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="newDepartmentName">اسم الإدارة الجديدة:</label>
          <input type="text" id="newDepartmentName" placeholder="أدخل اسم الإدارة" />
        </div>
      </div>
      <div class="modal-footer">
        <button onclick="addNewDepartment()" class="save-btn">إضافة</button>
        <button onclick="closeDepartmentModal()" class="cancel-btn">إلغاء</button>
      </div>
    </div>
  </div>

  <script src="dateUtils.js"></script>
  <script>
    const API_URL = localStorage.getItem('serverUrl') || 'http://localhost:5500/api';
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');

    // متغير لحفظ البيانات الأصلية للمقارنة
    let originalEmployeeData = {};

    if (!code) {
      alert('لم يتم تحديد كود الموظف');
      window.location.href = 'dashboard.html';
    }

    // دالة لتحميل بيانات الموظف
    async function loadEmployeeData() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${code}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (!response.ok) throw new Error('فشل في جلب البيانات');
        return await response.json();
      } catch (error) {
        console.error('Error:', error);
        alert('فشل في جلب بيانات الموظف');
        window.location.href = 'dashboard.html';
        return null;
      }
    }

    // تم حذف دوال تحميل الصور والمستندات لأن الحقول تم تحويلها إلى تواريخ العقد

    // تم حذف دوال الصور والمستندات لأن الحقول تم تحويلها إلى تواريخ العقد

    // دالة لحساب رصيد الإجازات
    function calculateLeaveBalance(hireDate) {
      if (!hireDate) return { balance: 7, used: 0, remaining: 7 };

      try {
        const hire = new Date(hireDate);
        const now = new Date();

        // حساب الفرق بالسنوات والأشهر
        let yearDiff = now.getFullYear() - hire.getFullYear();
        let monthDiff = now.getMonth() - hire.getMonth();

        if (monthDiff < 0) {
          yearDiff--;
          monthDiff += 12;
        }

        // حساب المدة بالأشهر للموظفين الجدد
        const totalMonths = yearDiff * 12 + monthDiff;
        const monthsOfService = totalMonths >= 0 ? totalMonths : 0;

        // تحديد رصيد الإجازات بناءً على مدة الخدمة
        let leaveBalance = 0;

        if (monthsOfService < 6) {
          // أقل من 6 أشهر: 7 أيام
          leaveBalance = 7;
        } else if (yearDiff < 10) {
          // من 6 أشهر إلى 10 سنوات: 21 يوم
          leaveBalance = 21;
        } else {
          // أكثر من 10 سنوات: 30 يوم
          leaveBalance = 30;
        }

        return {
          balance: leaveBalance,
          used: 0, // سيتم تحديثه من الخادم
          remaining: leaveBalance,
          yearsOfService: yearDiff,
          monthsOfService: monthsOfService
        };

      } catch (error) {
        console.error('خطأ في حساب رصيد الإجازات:', error);
        return { balance: 7, used: 0, remaining: 7 };
      }
    }

    // دالة لتحديث بيانات الإجازات من الخادم
    async function updateLeaveData(employeeCode) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${employeeCode}/leave-balance`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const leaveData = await response.json();

          // تحديث الحقول
          const form = document.getElementById('editEmployeeForm');
          if (form.elements['leave_balance']) {
            form.elements['leave_balance'].value = leaveData.calculated_balance || leaveData.current_balance || 0;
          }
          if (form.elements['leave_used']) {
            form.elements['leave_used'].value = leaveData.used_days || leaveData.current_used || 0;
          }
          if (form.elements['leave_remaining']) {
            form.elements['leave_remaining'].value = leaveData.remaining_days || leaveData.current_remaining || 0;
          }

          console.log('تم تحديث بيانات الإجازات من الخادم:', leaveData);
        } else {
          console.warn('فشل في تحميل بيانات الإجازات من الخادم');
        }
      } catch (error) {
        console.warn('خطأ في تحميل بيانات الإجازات:', error);
      }
    }

    // دالة لملء النموذج ببيانات الموظف
    async function fillFormWithEmployeeData(employee) {
      const form = document.getElementById('editEmployeeForm');

      // حفظ البيانات الأصلية للمقارنة لاحقاً
      originalEmployeeData = JSON.parse(JSON.stringify(employee));

      // قائمة حقول التاريخ التي تحتاج معالجة خاصة
      const dateFields = [
        'birth_date', 'hire_date', 'contract_start_date', 'contract_end_date', 'insurance_start', 'skill_start',
        'skill_end', 'vacation_date', 'start_date', 'end_date',
        'resignation_date', 'work_end_date', 'delivery_date',
        'penalty_date', 'evaluation_date'
      ];

      // Fill form with employee data
      Object.keys(employee).forEach(key => {
        const input = form.elements[key];
        if (input) {
          if (dateFields.includes(key)) {
            // معالجة خاصة لحقول التاريخ باستخدام الدالة المحسنة
            const formattedDate = formatDateForInput(employee[key]) || '';
            input.value = formattedDate;
            // حفظ التاريخ المنسق في البيانات الأصلية للمقارنة
            originalEmployeeData[key] = formattedDate;
          } else {
            input.value = employee[key] || '';
          }
        }
      });

      // تحديث بيانات الإجازات من الخادم
      if (employee.code) {
        await updateLeaveData(employee.code);
      } else {
        // حساب رصيد الإجازات محلياً إذا لم يتوفر من الخادم
        const leaveData = calculateLeaveBalance(employee.hire_date);
        if (form.elements['leave_balance']) {
          form.elements['leave_balance'].value = employee.leave_balance || leaveData.balance;
        }
        if (form.elements['leave_used']) {
          form.elements['leave_used'].value = employee.leave_used || leaveData.used;
        }
        if (form.elements['leave_remaining']) {
          form.elements['leave_remaining'].value = employee.leave_remaining || leaveData.remaining;
        }
      }

      // حفظ بيانات الإجازات في البيانات الأصلية
      originalEmployeeData.leave_balance = form.elements['leave_balance']?.value || '';
      originalEmployeeData.leave_used = form.elements['leave_used']?.value || '';
      originalEmployeeData.leave_remaining = form.elements['leave_remaining']?.value || '';

      // حساب المدة المتبقية من العقد
      const contractEndInput = document.getElementById('contract_end_date');
      const contractStartInput = document.querySelector('input[name="contract_start_date"]');
      if (contractEndInput && contractEndInput.value) {
        const startDate = contractStartInput ? contractStartInput.value : null;
        const remaining = calculateContractRemaining(contractEndInput.value, startDate);
        document.getElementById('contract_remaining').value = remaining;
      }
    }

    // تحميل بيانات الموظف عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', async function () {
      // تحميل قائمة الإدارات أولاً
      await loadDepartments();

      const employee = await loadEmployeeData();
      if (employee) {
        await fillFormWithEmployeeData(employee);
      }

      // إعداد النافذة المنبثقة للإدارة
      setupDepartmentModal();

      // إضافة معالج حدث لزر التحديث
      document.getElementById('refreshBtn').addEventListener('click', async function () {
        const employee = await loadEmployeeData();
        if (employee) {
          await fillFormWithEmployeeData(employee);
          // حساب المدة المتبقية بعد تحميل البيانات
          const contractEndInput = document.getElementById('contract_end_date');
          const contractStartInput = document.querySelector('input[name="contract_start_date"]');
          if (contractEndInput && contractEndInput.value) {
            const startDate = contractStartInput ? contractStartInput.value : null;
            const remaining = calculateContractRemaining(contractEndInput.value, startDate);
            document.getElementById('contract_remaining').value = remaining;
          }
          alert('تم تحديث البيانات بنجاح');
        }
      });

      // إضافة مستمع للأحداث لحقول تاريخ العقد
      const contractEndInput = document.getElementById('contract_end_date');
      const contractStartInput = document.querySelector('input[name="contract_start_date"]');
      const contractRemainingInput = document.getElementById('contract_remaining');

      function updateContractRemaining() {
        if (contractEndInput && contractRemainingInput) {
          const startDate = contractStartInput ? contractStartInput.value : null;
          const endDate = contractEndInput.value;
          const remaining = calculateContractRemaining(endDate, startDate);
          contractRemainingInput.value = remaining;
        }
      }

      if (contractEndInput && contractRemainingInput) {
        contractEndInput.addEventListener('change', updateContractRemaining);
      }

      if (contractStartInput && contractRemainingInput) {
        contractStartInput.addEventListener('change', updateContractRemaining);
      }
    });

    // دالة لحساب المدة المتبقية من العقد
    function calculateContractRemaining(endDate, startDate) {
      // إذا لم يتم تمرير تاريخ البداية، استخدم تاريخ بداية العقد من النموذج
      if (!startDate) {
        const contractStartInput = document.querySelector('input[name="contract_start_date"]');
        startDate = contractStartInput ? contractStartInput.value : null;
      }

      console.log('حساب مدة العقد:', { startDate, endDate });

      // استخدام الدالة المحسنة من dateUtils.js
      if (typeof DateUtils !== 'undefined' && DateUtils.calculateContractDuration) {
        const result = DateUtils.calculateContractDuration(startDate, endDate);
        console.log('نتيجة حساب مدة العقد:', result);
        return result.formattedText;
      }

      // Fallback للحالات التي لا تتوفر فيها dateUtils.js
      if (!endDate) {
        return "";
      }

      const today = new Date();
      const contractEnd = new Date(endDate);

      if (contractEnd < today) {
        return "انتهى العقد";
      }

      const diffTime = contractEnd - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return `المتبقي: ${diffDays} يوم`;
    }

    // دالة لتنسيق التاريخ بشكل موحد للمقارنة
    function normalizeDate(dateValue) {
      if (!dateValue) return '';

      try {
        // إذا كان التاريخ في صيغة YYYY-MM-DD فهو جاهز
        if (typeof dateValue === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
          return dateValue;
        }

        // معالجة التواريخ التي تحتوي على وقت (مثل: 2018-12-01T00:00:00.000Z أو 2018-12-01T12:00:00)
        if (typeof dateValue === 'string' && dateValue.includes('T')) {
          const datePart = dateValue.split('T')[0];
          if (/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
            return datePart;
          }
        }

        // معالجة خاصة للتواريخ العربية (مثل: ٠١‏/١٢‏/٢٠١٨، ١٢:٠٠ ص)
        if (typeof dateValue === 'string' && dateValue.includes('‏')) {
          try {
            // محاولة تحويل التاريخ العربي إلى تاريخ عادي
            const arabicDate = new Date(dateValue);
            if (!isNaN(arabicDate.getTime())) {
              const year = arabicDate.getFullYear();
              const month = String(arabicDate.getMonth() + 1).padStart(2, '0');
              const day = String(arabicDate.getDate()).padStart(2, '0');
              return `${year}-${month}-${day}`;
            }
          } catch (error) {
            console.warn('خطأ في معالجة التاريخ العربي:', dateValue);
          }
        }

        // معالجة التواريخ بصيغة أخرى مع تجنب مشاكل المنطقة الزمنية
        let date;
        if (typeof dateValue === 'string') {
          // إذا كان النص يحتوي على تاريخ فقط، أضف وقت منتصف النهار لتجنب مشاكل المنطقة الزمنية
          if (/^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
            date = new Date(dateValue + 'T12:00:00');
          } else {
            date = new Date(dateValue);
          }
        } else {
          date = new Date(dateValue);
        }

        if (isNaN(date.getTime())) return '';

        // استخدام التوقيت المحلي لتجنب مشاكل المنطقة الزمنية
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        console.warn('خطأ في تنسيق التاريخ:', dateValue, error);
        return '';
      }
    }

    // دالة محسنة لتنسيق التاريخ للإدخال
    function formatDateForInput(dateValue) {
      return normalizeDate(dateValue);
    }

    // دالة لمقارنة البيانات والتحقق من وجود تغييرات حقيقية
    function hasRealChanges(originalData, newData) {
      // قائمة الحقول التي يجب تجاهلها في المقارنة (الحقول المحسوبة تلقائياً)
      const ignoredFields = [
        'leave_balance', 'leave_used', 'leave_remaining', 'contract_remaining'
      ];

      // قائمة حقول التاريخ للمعالجة الخاصة
      const dateFields = [
        'birth_date', 'hire_date', 'contract_start_date', 'contract_end_date', 'insurance_start', 'skill_start',
        'skill_end', 'vacation_date', 'start_date', 'end_date',
        'resignation_date', 'work_end_date', 'delivery_date',
        'penalty_date', 'evaluation_date'
      ];

      let hasChanges = false;
      const changes = [];

      // مقارنة كل حقل
      Object.keys(newData).forEach(key => {
        // تجاهل الحقول المحسوبة تلقائياً
        if (ignoredFields.includes(key)) {
          return;
        }

        const originalValue = originalData[key] || '';
        const newValue = newData[key] || '';

        // معالجة خاصة لحقول التاريخ
        if (dateFields.includes(key)) {
          // تنسيق التواريخ للمقارنة بشكل موحد
          const originalNormalized = normalizeDate(originalValue);
          const newNormalized = normalizeDate(newValue);

          // تسجيل مفصل للتواريخ لتتبع المشاكل
          if (originalValue || newValue) {
            console.log(`🗓️ مقارنة التاريخ ${key}:`, {
              original: originalValue,
              originalNormalized,
              new: newValue,
              newNormalized,
              areEqual: originalNormalized === newNormalized
            });
          }

          if (originalNormalized !== newNormalized) {
            hasChanges = true;
            changes.push(`${key}: من "${originalValue}" (${originalNormalized}) إلى "${newValue}" (${newNormalized})`);
          }
        } else {
          // مقارنة عادية للحقول الأخرى
          if (String(originalValue).trim() !== String(newValue).trim()) {
            hasChanges = true;
            changes.push(`${key}: من "${originalValue}" إلى "${newValue}"`);
          }
        }
      });

      return {
        hasChanges,
        changes,
        changeCount: changes.length
      };
    }

    // Handle form submission
    document.getElementById('editEmployeeForm').addEventListener('submit', async function (e) {
      e.preventDefault();

      try {
        // حفظ البيانات الأساسية أولاً
        const formData = new FormData(e.target);
        const data = {};

        // قائمة حقول التاريخ التي تحتاج معالجة خاصة
        const dateFields = [
          'birth_date', 'hire_date', 'contract_start_date', 'contract_end_date', 'insurance_start', 'skill_start',
          'skill_end', 'vacation_date', 'start_date', 'end_date',
          'resignation_date', 'work_end_date', 'delivery_date',
          'penalty_date', 'evaluation_date'
        ];

        formData.forEach((value, key) => {
          // معالجة خاصة لحقول التاريخ (تجنب مشاكل المنطقة الزمنية)
          if (dateFields.includes(key) && value) {
            // استخدام التاريخ مباشرة بدون تحويل لتجنب مشاكل المنطقة الزمنية
            data[key] = value;
          } else {
            data[key] = value;
          }
        });

        // التحقق من وجود تغييرات حقيقية
        const changeAnalysis = hasRealChanges(originalEmployeeData, data);

        if (!changeAnalysis.hasChanges) {
          alert('لم يتم إجراء أي تغييرات على بيانات الموظف');
          return;
        }

        // عرض التغييرات للمستخدم للتأكيد (اختياري)
        const showConfirmation = changeAnalysis.changeCount > 3; // عرض التأكيد فقط للتغييرات الكثيرة

        if (showConfirmation) {
          const confirmMessage = `تم اكتشاف ${changeAnalysis.changeCount} تغيير(ات). هل تريد المتابعة؟\n\n` +
            `أمثلة على التغييرات:\n${changeAnalysis.changes.slice(0, 3).join('\n')}` +
            (changeAnalysis.changes.length > 3 ? '\n... والمزيد' : '');

          if (!confirm(confirmMessage)) {
            return;
          }
        }

        console.log('📅 بيانات الموظف مع التواريخ المحولة:', data);
        console.log('🔍 تحليل التغييرات:', changeAnalysis);

        // إنشاء كائن يحتوي فقط على الحقول المتغيرة
        const changedData = { code: data.code }; // نحتاج الكود دائماً

        // إضافة الحقول المتغيرة فقط
        Object.keys(data).forEach(key => {
          const originalValue = originalEmployeeData[key] || '';
          const newValue = data[key] || '';

          // تحقق من التغيير باستخدام نفس منطق المقارنة
          const dateFields = [
            'birth_date', 'hire_date', 'contract_start_date', 'contract_end_date', 'insurance_start', 'skill_start',
            'skill_end', 'vacation_date', 'start_date', 'end_date',
            'resignation_date', 'work_end_date', 'delivery_date',
            'penalty_date', 'evaluation_date'
          ];

          let hasChanged = false;

          if (dateFields.includes(key)) {
            const originalNormalized = normalizeDate(originalValue);
            const newNormalized = normalizeDate(newValue);
            hasChanged = originalNormalized !== newNormalized;
          } else {
            hasChanged = String(originalValue).trim() !== String(newValue).trim();
          }

          if (hasChanged && key !== 'code') {
            changedData[key] = data[key];
          }
        });

        console.log('📝 البيانات المتغيرة فقط:', changedData);

        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/${code}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(changedData)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'فشل في حفظ التعديلات');
        }

        const result = await response.json();

        // تحديث البيانات الأصلية بعد الحفظ الناجح
        originalEmployeeData = JSON.parse(JSON.stringify(data));

        alert(`تم حفظ التعديلات بنجاح!\n\nعدد الحقول المحدثة: ${Object.keys(changedData).length - 1}`);

        // إعادة تحميل البيانات لضمان التحديث
        const updatedEmployee = await loadEmployeeData();
        if (updatedEmployee) {
          await fillFormWithEmployeeData(updatedEmployee);
        }

      } catch (error) {
        console.error('Error:', error);
        alert('فشل في حفظ التعديلات: ' + error.message);
      }
    });
  </script>



  <script>
    // دالة تسجيل الخروج
    function logout() {
      if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح البيانات المحفوظة
        localStorage.removeItem('token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('activeSection');
        localStorage.removeItem('userName');

        // إعادة التوجيه لصفحة تسجيل الدخول
        window.location.href = 'login.html';
      }
    }

    // تم حذف دالة updateSidebarUserInfo المتعارضة - يتم التحكم بها من sidebar-standalone.js فقط

    // دالة إلغاء التعديل
    function cancelEdit() {
      if (confirm('هل أنت متأكد من إلغاء التعديل؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        // العودة إلى الصفحة السابقة أو الصفحة الرئيسية
        if (window.history.length > 1) {
          window.history.back();
        } else {
          window.location.href = 'dashboard.html';
        }
      }
    }

    // تحميل قائمة الإدارات
    async function loadDepartments() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees/departments`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        const departments = await response.json();

        const departmentSelect = document.getElementById('departmentSelect');
        departmentSelect.innerHTML = '<option value="">اختر الإدارة</option>';

        departments.forEach(dept => {
          const option = document.createElement('option');
          option.value = dept;
          option.textContent = dept;
          departmentSelect.appendChild(option);
        });

        // إضافة خيار "إدارة جديدة"
        const newDeptOption = document.createElement('option');
        newDeptOption.value = 'new_department';
        newDeptOption.textContent = '+ إدارة جديدة';
        newDeptOption.style.fontWeight = 'bold';
        newDeptOption.style.color = '#667eea';
        departmentSelect.appendChild(newDeptOption);

      } catch (error) {
        console.error('خطأ في تحميل الأقسام:', error);
      }
    }

    // إعداد النافذة المنبثقة للإدارة
    function setupDepartmentModal() {
      const departmentSelect = document.getElementById('departmentSelect');

      departmentSelect.addEventListener('change', function () {
        if (this.value === 'new_department') {
          document.getElementById('departmentModal').style.display = 'block';
          document.getElementById('newDepartmentName').focus();
        }
      });

      // إغلاق النافذة عند النقر خارجها
      window.addEventListener('click', function (event) {
        const modal = document.getElementById('departmentModal');
        if (event.target === modal) {
          closeDepartmentModal();
        }
      });

      // إغلاق النافذة عند الضغط على Escape
      document.addEventListener('keydown', function (event) {
        if (event.key === 'Escape') {
          closeDepartmentModal();
        }
      });
    }

    // إغلاق نافذة الإدارة
    function closeDepartmentModal() {
      document.getElementById('departmentModal').style.display = 'none';
      document.getElementById('newDepartmentName').value = '';

      // إعادة تعيين قائمة الإدارات للخيار الافتراضي
      const departmentSelect = document.getElementById('departmentSelect');
      departmentSelect.value = '';
    }

    // إضافة إدارة جديدة
    async function addNewDepartment() {
      const newDepartmentName = document.getElementById('newDepartmentName').value.trim();

      if (!newDepartmentName) {
        alert('يرجى إدخال اسم الإدارة');
        return;
      }

      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/department-permissions/departments`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            name: newDepartmentName,
            description: `إدارة ${newDepartmentName}`
          })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.message || 'فشل في إضافة الإدارة');
        }

        // إضافة الإدارة الجديدة إلى القائمة
        const departmentSelect = document.getElementById('departmentSelect');
        const option = document.createElement('option');
        option.value = newDepartmentName;
        option.textContent = newDepartmentName;

        // إدراج الخيار الجديد قبل "إدارة جديدة"
        const newDeptOption = departmentSelect.querySelector('option[value="new_department"]');
        departmentSelect.insertBefore(option, newDeptOption);

        // اختيار الإدارة الجديدة
        departmentSelect.value = newDepartmentName;

        // إغلاق النافذة
        closeDepartmentModal();

        alert(data.message || 'تم إضافة الإدارة بنجاح');

      } catch (error) {
        console.error('خطأ في إضافة الإدارة:', error);
        alert('فشل في إضافة الإدارة: ' + error.message);
      }
    }


  </script>
</body>

</html>