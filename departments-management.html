<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإدارات - نظام إدارة الموظفين</title>
    <link rel="stylesheet" href="shared-styles.css">
    <link rel="stylesheet" href="departments-management.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="header-title">
                    <i class="fas fa-building"></i>
                    <h1>إدارة الإدارات</h1>
                </div>
                <div class="header-actions">
                    <button id="addDepartmentBtn" class="btn btn-success">
                        <i class="fas fa-plus"></i>
                        إضافة إدارة جديدة
                    </button>
                    <button id="backBtn" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للرئيسية
                    </button>
                    <button id="logoutBtn" class="btn btn-danger">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Loading Indicator -->
            <div class="loading" id="loading" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i>
                جاري التحميل...
            </div>

            <!-- Departments Table Section -->
            <section class="departments-section">
                <div class="section-header">
                    <h2><i class="fas fa-list"></i> قائمة الإدارات</h2>
                    <button id="refreshBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
                
                <div class="table-container">
                    <table id="departmentsTable" class="table table-striped">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>اسم الإدارة</th>
                                <th>الوصف</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="departmentsTableBody">
                            <!-- سيتم ملء البيانات هنا ديناميكياً -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Modal لإضافة/تعديل إدارة -->
    <div id="departmentModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة إدارة جديدة</h2>
                <span class="close" id="closeModal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="departmentForm">
                    <input type="hidden" id="departmentId">
                    <div class="form-group">
                        <label for="departmentName">اسم الإدارة <span class="required">*</span></label>
                        <input type="text" id="departmentName" required placeholder="أدخل اسم الإدارة">
                    </div>
                    <div class="form-group">
                        <label for="departmentDescription">وصف الإدارة</label>
                        <textarea id="departmentDescription" rows="3" placeholder="أدخل وصف الإدارة (اختياري)"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="saveDepartmentBtn" class="btn btn-success">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
                <button type="button" id="cancelBtn" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تأكيد الحذف</h2>
                <span class="close" id="closeDeleteModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>هل أنت متأكد من حذف الإدارة "<span id="departmentToDelete"></span>"؟</p>
                    <p class="warning-text">تحذير: سيؤثر هذا على جميع الموظفين المرتبطين بهذه الإدارة!</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="confirmDeleteBtn" class="btn btn-danger">
                    <i class="fas fa-trash"></i>
                    حذف
                </button>
                <button type="button" id="cancelDeleteBtn" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- Message Container -->
    <div id="messageContainer" class="message-container"></div>

    <script src="departments-management.js"></script>
</body>
</html>
