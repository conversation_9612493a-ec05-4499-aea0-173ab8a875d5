// متغيرات عامة
let API_URL = localStorage.getItem('serverUrl') || 'http://localhost:5500';

// التأكد من أن API_URL لا يحتوي على /api في النهاية
if (API_URL.endsWith('/api')) {
  API_URL = API_URL.slice(0, -4);
}
let custodyItems = [];
let deliveryRecords = []; // سجلات التسليم المفلترة للعرض
let allDeliveryRecords = []; // جميع سجلات التسليم للحساب

let employees = [];
let nextCustodyCode = 10000;
let nextOperationNumber = 1;
let userPermissions = {}; // متغير لتخزين صلاحيات المستخدم

// عناصر DOM
const tabBtns = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    // تحميل صلاحيات المستخدم أولاً
    await loadUserPermissions();

    await initializePage();
    setupEventListeners();
    setupTabSwitching();
    setupModalEventListeners();

    // إعداد قائمة اقتراحات للبحث في تسليم العهد
    setupDeliverySearch();

    // تعيين التاريخ الحالي
    setCurrentDate();

    // تطبيق الصلاحيات على الأزرار بعد تحميل البيانات وعرضها
    setupAllActionButtons();

    // التحقق من المحتوى المحدد من البطاقات بعد تحميل الصفحة
    setTimeout(() => {
        checkSelectedContent();
    }, 100);
});

// تهيئة الصفحة
async function initializePage() {
    try {
        await loadEmployees();
        await loadDepartments();
        await loadCustodyItems();
        await loadDeliveryRecords(); // سجلات مفلترة للعرض
        await loadAllDeliveryRecordsForCalculation(); // جميع السجلات للحساب

        // حساب الكميات المتاحة بعد تحميل جميع البيانات
        calculateAvailableQuantities();

        await generateNextCustodyCode();
        await generateNextOperationNumber();

        // تهيئة DataTables للعهد وتسليم العهد بعد تأخير قصير للتأكد من تحميل jQuery
        setTimeout(() => {
            initializeCustodyDataTables();
            initializeDeliveryDataTables();

            // إعداد فلاتر البحث المتقدمة
            setupAdvancedCustodySearch();
            setupAdvancedDeliverySearch();
        }, 1000);

        // displayDeliveryRecords(deliveryRecords); // تم استبداله بـ DataTables
        displayUndeliveredItems();
        loadAvailableCustodyForDelivery();
    } catch (error) {
        console.error('خطأ في تهيئة الصفحة:', error);
        showNotification('خطأ في تحميل البيانات', 'error');
    }
}

// تحميل الموظفين
async function loadEmployees() {
    try {
        // إضافة معامل لاستبعاد الموظفين المستقيلين
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/employees?include_resigned=false`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            employees = await response.json();

            // تحديث اقتراحات أسماء الموظفين
            updateEmployeeNameSuggestions();
        }
    } catch (error) {

    }
}

// تحديث اقتراحات أسماء الموظفين
function updateEmployeeNameSuggestions() {
    const datalist = document.getElementById('employeeNameSuggestions');
    if (!datalist) return;
    
    // مسح الاقتراحات الحالية
    datalist.innerHTML = '';
    
    // إضافة اقتراحات جديدة
    employees.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.full_name;
        option.setAttribute('data-code', employee.code);
        datalist.appendChild(option);
    });
}

// تحميل صلاحيات المستخدم - استخدام النظام الموحد
async function loadUserPermissions() {
    try {
        // استخدام النظام الموحد من permissions.js
        if (window.permissionManager) {
            // لا حاجة لتحميل منفصل، النظام الموحد يتولى ذلك
            console.log('تم استخدام النظام الموحد للصلاحيات');
        } else {
            // تحميل الصلاحيات من localStorage كنسخة احتياطية
            const permissionsStr = localStorage.getItem('permissions');
            if (permissionsStr) {
                userPermissions = JSON.parse(permissionsStr);
            } else {
                userPermissions = {
                    can_edit: false,
                    can_delete: false,
                    can_add: false
                };
            }
            console.log('تم تحميل صلاحيات المستخدم محلياً:', userPermissions);
        }
    } catch (error) {
        console.error('خطأ في تحميل صلاحيات المستخدم:', error);
        userPermissions = {
            can_edit: false,
            can_delete: false,
            can_add: false
        };
    }
}

// تطبيق الصلاحيات على جميع الأزرار
function setupAllActionButtons() {
    setupCustodyActionButtons();
    setupDeliveryActionButtons();
    setupReturnActionButtons();
}

// تطبيق الصلاحيات على أزرار جدول العهد
function setupCustodyActionButtons() {
    // انتظار قصير للتأكد من تحميل النظام الموحد
    setTimeout(() => {
        applyPermissionsToButtons('custodyTableBody');
        // استخدام النظام الموحد أيضاً
        if (window.permissionManager && typeof window.permissionManager.applyPermissionsToTable === 'function') {
            window.permissionManager.applyPermissionsToTable('custodyTableBody');
        }
    }, 50);
}

// تطبيق الصلاحيات على أزرار جدول التسليم
function setupDeliveryActionButtons() {
    setTimeout(() => {
        applyPermissionsToButtons('deliveryTableBody');
        // استخدام النظام الموحد أيضاً
        if (window.permissionManager && typeof window.permissionManager.applyPermissionsToTable === 'function') {
            window.permissionManager.applyPermissionsToTable('deliveryTableBody');
        }
    }, 50);
}

// تطبيق الصلاحيات على أزرار جدول الاسترجاع
function setupReturnActionButtons() {
    setTimeout(() => {
        applyPermissionsToButtons('returnTableBody');
        // استخدام النظام الموحد أيضاً
        if (window.permissionManager && typeof window.permissionManager.applyPermissionsToTable === 'function') {
            window.permissionManager.applyPermissionsToTable('returnTableBody');
        }
    }, 50);
}



// تطبيق الصلاحيات على الأزرار في جدول محدد
function applyPermissionsToButtons(tableBodyId) {
    const tableBody = document.getElementById(tableBodyId);
    if (!tableBody) return;

    // استثناء قسم إدارة المستخدمين من تطبيق الصلاحيات
    if (tableBodyId === 'usersTableBody') {
        return;
    }

    // البحث عن جميع الأزرار التي تحتاج إلى صلاحيات
    const buttons = tableBody.querySelectorAll('[data-permission]');

    buttons.forEach(button => {
        const permission = button.getAttribute('data-permission');

        if (permission) {
            // استخدام النظام الموحد للتحقق من الصلاحيات
            let hasPermissionValue = false;

            if (window.permissionManager && typeof window.permissionManager.hasPermission === 'function') {
                // استخدام النظام الموحد
                hasPermissionValue = window.permissionManager.hasPermission(permission);
            } else if (typeof hasPermission === 'function') {
                // استخدام الدالة المساعدة
                hasPermissionValue = hasPermission(permission);
            } else if (userPermissions.hasOwnProperty(permission)) {
                // استخدام النظام المحلي كنسخة احتياطية
                hasPermissionValue = userPermissions[permission];
            }

            // تطبيق الصلاحية
            if (!hasPermissionValue) {
                button.style.display = 'none';
            } else {
                button.style.display = 'inline-block';
            }
        }
    });
}

// تحميل الإدارات
function loadDepartments() {
    const departmentFilter = document.getElementById('departmentFilter');
    if (!departmentFilter) return; // إذا لم يوجد العنصر لا تفعل شيئًا
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))];
    departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
    departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentFilter.appendChild(option);
    });
}

// تحميل العهد
async function loadCustodyItems() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            custodyItems = await response.json();
            console.log('تم تحميل العهد:', custodyItems.length);
        }
    } catch (error) {
        console.error('خطأ في تحميل العهد:', error);
        custodyItems = [];
    }
}

// حساب الكميات المتاحة لجميع العهد
function calculateAvailableQuantities() {
    console.log('🔄 بدء حساب الكميات المتاحة...');
    console.log(`📊 استخدام ${allDeliveryRecords.length} سجل تسليم للحساب (جميع الإدارات)`);
    console.log(`📋 عرض ${deliveryRecords.length} سجل تسليم مفلتر للعرض`);

    custodyItems.forEach(item => {
        // حساب الكميات المسلمة حالياً باستخدام جميع سجلات التسليم (بدون فلترة الإدارات)
        const currentlyDelivered = allDeliveryRecords
            .filter(rec => rec.custody_code == item.custody_code && rec.status === 'مسلم')
            .reduce((sum, rec) => sum + (parseInt(rec.quantity) || 0), 0);

        // الكمية المتاحة = الكمية الأصلية - الكميات المسلمة حالياً
        item.available_quantity = item.quantity - currentlyDelivered;

        // التأكد من أن الكمية المتاحة لا تقل عن الصفر
        if (item.available_quantity < 0) {
            item.available_quantity = 0;
        }

        // تسجيل مفصل للعهد التي لها كميات مسلمة
        if (currentlyDelivered > 0) {
            console.log(`📊 العهدة ${item.custody_code}: الأصلية=${item.quantity}, المسلمة=${currentlyDelivered}, المتاحة=${item.available_quantity}`);
        }
    });

    console.log('✅ تم حساب الكميات المتاحة باستخدام جميع سجلات التسليم');
    console.log(`📈 العهد المتاحة للتسليم: ${custodyItems.filter(item => item.available_quantity > 0).length}`);
    console.log(`📊 إجمالي العهد: ${custodyItems.length}`);
}

// تحميل سجلات التسليم (المفلترة للعرض)
async function loadDeliveryRecords() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody/delivery`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            deliveryRecords = await response.json();
            console.log('✅ تم تحميل سجلات التسليم المفلترة:', deliveryRecords.length);
        } else {
            console.error('❌ خطأ في تحميل سجلات التسليم:', response.status);
            deliveryRecords = [];
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل سجلات التسليم:', error);
        deliveryRecords = [];
    }
}

// تحميل جميع سجلات التسليم للحساب (بدون فلترة الإدارات)
async function loadAllDeliveryRecordsForCalculation() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody/delivery/all-for-calculation`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            allDeliveryRecords = await response.json();
            console.log('📊 تم تحميل جميع سجلات التسليم للحساب:', allDeliveryRecords.length);
        } else {
            console.error('❌ خطأ في تحميل سجلات التسليم للحساب:', response.status);
            allDeliveryRecords = [];
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل سجلات التسليم للحساب:', error);
        allDeliveryRecords = [];
    }
}

// دالة شاملة لإعادة تحميل جميع البيانات وإعادة حساب الكميات
async function refreshAllData() {
    console.log('🔄 إعادة تحميل جميع البيانات...');

    try {
        // تحميل البيانات بالتسلسل لضمان التطابق
        await loadCustodyItems();
        await loadDeliveryRecords(); // سجلات مفلترة للعرض
        await loadAllDeliveryRecordsForCalculation(); // جميع السجلات للحساب

        // إعادة حساب الكميات المتاحة
        calculateAvailableQuantities();

        // تحديث جميع الواجهات
        displayCustodyItemsWithDataTables();
        displayUndeliveredItems();
        loadAvailableCustodyForDelivery();

        console.log('✅ تم تحديث جميع البيانات بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تحديث البيانات:', error);
    }
}



// توليد كود العهد التالي
async function generateNextCustodyCode() {
    if (custodyItems.length > 0) {
        const maxCode = Math.max(...custodyItems.map(item => parseInt(item.custody_code)));
        nextCustodyCode = Math.max(maxCode + 1, 10000);
    } else {
        nextCustodyCode = 10000;
    }
    const custodyCodeField = document.getElementById('custodyCode');
    custodyCodeField.value = nextCustodyCode;
    custodyCodeField.readOnly = true;
}

// توليد رقم العملية التالي
async function generateNextOperationNumber() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody/delivery/operation-numbers`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            const operationNumbers = await response.json();
            if (operationNumbers.length > 0) {
                const maxOperation = Math.max(...operationNumbers.map(record => parseInt(record.operation_number)));
                nextOperationNumber = maxOperation + 1;
            }
        }
    } catch (error) {
        console.error('خطأ في جلب أرقام العمليات:', error);
    }
    document.getElementById('operationNumber').value = nextOperationNumber;
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // أزرار الإخفاء والإظهار للعهد
    const toggleCustodyFormBtn = document.getElementById('toggleCustodyFormBtn');
    const custodyFormContainer = document.getElementById('custodyFormContainer');
    const toggleCustodyIcon = document.getElementById('toggleCustodyIcon');
    const toggleCustodyArrow = document.getElementById('toggleCustodyArrow');

    if (toggleCustodyFormBtn && custodyFormContainer) {
        custodyFormContainer.style.display = 'none';

        toggleCustodyFormBtn.addEventListener('click', function() {
            const isHidden = custodyFormContainer.style.display === 'none';
            custodyFormContainer.style.display = isHidden ? 'block' : 'none';

            if (toggleCustodyIcon) {
                toggleCustodyIcon.className = isHidden ? 'fas fa-minus-circle' : 'fas fa-plus-circle';
            }
            if (toggleCustodyArrow) {
                toggleCustodyArrow.className = isHidden ? 'fas fa-chevron-up toggle-arrow' : 'fas fa-chevron-down toggle-arrow';
            }
        });
    }

    // أزرار الإخفاء والإظهار لتسليم العهدة
    const toggleDeliveryFormBtn = document.getElementById('toggleDeliveryFormBtn');
    const deliveryFormContainer = document.getElementById('deliveryFormContainer');
    const toggleDeliveryIcon = document.getElementById('toggleDeliveryIcon');
    const toggleDeliveryArrow = document.getElementById('toggleDeliveryArrow');

    if (toggleDeliveryFormBtn && deliveryFormContainer) {
        deliveryFormContainer.style.display = 'none';

        toggleDeliveryFormBtn.addEventListener('click', function() {
            const isHidden = deliveryFormContainer.style.display === 'none';
            deliveryFormContainer.style.display = isHidden ? 'block' : 'none';

            if (toggleDeliveryIcon) {
                toggleDeliveryIcon.className = isHidden ? 'fas fa-minus-circle' : 'fas fa-plus-circle';
            }
            if (toggleDeliveryArrow) {
                toggleDeliveryArrow.className = isHidden ? 'fas fa-chevron-up toggle-arrow' : 'fas fa-chevron-down toggle-arrow';
            }
        });
    }

    // زر إخفاء فلاتر العهد
    const toggleCustodyFiltersBtn = document.getElementById('toggleCustodyFiltersBtn');
    const custodyFiltersContainer = document.getElementById('custodyFiltersContainer');
    const toggleCustodyFiltersArrow = document.getElementById('toggleCustodyFiltersArrow');

    if (toggleCustodyFiltersBtn && custodyFiltersContainer) {
        toggleCustodyFiltersBtn.addEventListener('click', function() {
            const isHidden = custodyFiltersContainer.style.display === 'none';
            custodyFiltersContainer.style.display = isHidden ? 'block' : 'none';

            if (toggleCustodyFiltersArrow) {
                toggleCustodyFiltersArrow.className = isHidden ? 'fas fa-chevron-up toggle-arrow' : 'fas fa-chevron-down toggle-arrow';
            }
        });
    }

    // زر إخفاء فلاتر تسليم العهد
    const toggleDeliveryFiltersBtn = document.getElementById('toggleDeliveryFiltersBtn');
    const deliveryFiltersContainer = document.getElementById('deliveryFiltersContainer');
    const toggleDeliveryFiltersArrow = document.getElementById('toggleDeliveryFiltersArrow');

    if (toggleDeliveryFiltersBtn && deliveryFiltersContainer) {
        toggleDeliveryFiltersBtn.addEventListener('click', function() {
            const isHidden = deliveryFiltersContainer.style.display === 'none';
            deliveryFiltersContainer.style.display = isHidden ? 'block' : 'none';

            if (toggleDeliveryFiltersArrow) {
                toggleDeliveryFiltersArrow.className = isHidden ? 'fas fa-chevron-up toggle-arrow' : 'fas fa-chevron-down toggle-arrow';
            }
        });
    }

    // دالة مساعدة لإضافة مستمع حدث مع فحص وجود العنصر
    function addEventListenerSafe(elementId, event, handler) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(event, handler);
        } else {
            console.warn(`Element with ID '${elementId}' not found`);
        }
    }
    
    // نموذج إضافة عهدة
    addEventListenerSafe('addCustodyForm', 'submit', handleAddCustody);
    
    // نموذج تسليم عهدة
    addEventListenerSafe('deliverCustodyForm', 'submit', handleDeliverCustody);
    

    
    // ملء كود الموظف تلقائيًا عند اختيار اسم من القائمة
    addEventListenerSafe('employeeCodeDeliver', 'input', handleEmployeeNameSelection);
    addEventListenerSafe('employeeCodeDeliver', 'change', handleEmployeeNameSelection);
    
    // تغيير كود العهد في التسليم
    addEventListenerSafe('custodyCodeDeliver', 'change', updateCustodyDetails);
    
    // بحث في العهد - تم تعطيله لاستخدام DataTables
    // addEventListenerSafe('searchCustody', 'input', function() {
    //     console.log('البحث في العهد:', this.value);
    //     filterCustodyItems();
    // });
    
    // بحث مباشر في العهد للتسليم
    addEventListenerSafe('searchCustodyDeliver', 'input', function() {
        const searchTerm = this.value.trim();
        loadAvailableCustodyForDelivery(searchTerm);
    });
    
    // تم إزالة مستمع الحدث للعنصر غير الموجود 'searchCustodyBtn'
    
    const searchCustodyDeliver = document.getElementById('searchCustodyDeliver');
    if (searchCustodyDeliver) {
        searchCustodyDeliver.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchCustody();
            }
        });
        // إضافة مستمع حدث للتغيير في حقل البحث
        searchCustodyDeliver.addEventListener('input', function() {
            searchCustody();
        });
    }
    
    // بحث في التسليم
    addEventListenerSafe('searchDeliveryBtn', 'click', filterDeliveryRecords);
    

    
    // تحديث العهد غير المسلمة
    addEventListenerSafe('refreshUndeliveredBtn', 'click', displayUndeliveredItems);
    
    // تصدير البيانات
    addEventListenerSafe('exportCustodyBtn', 'click', () => exportToExcel('custody'));
    addEventListenerSafe('exportUndeliveredBtn', 'click', () => exportToExcel('undelivered'));
    addEventListenerSafe('exportDeliveryBtn', 'click', () => exportToExcel('delivery'));
    // addEventListenerSafe('exportReturnBtn', 'click', () => exportToExcel('return')); // تم تعطيله مؤقتاً
    
    // تقرير عهدة الموظف
    addEventListenerSafe('searchEmployeeReportBtn', 'click', searchEmployeeForReport);
    
    const employeeReportSearch = document.getElementById('employeeReportSearch');
    if (employeeReportSearch) {
        // مستمع حدث للبحث عند الضغط على Enter
        employeeReportSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchEmployeeForReport();
            }
        });
        
        // مستمع حدث للبحث المباشر عند كتابة الحروف
        employeeReportSearch.addEventListener('input', function() {
            searchEmployeeForReport();
        });
    }
    
    addEventListenerSafe('printEmployeeCustodyBtn', 'click', printEmployeeCustodyReport);
    addEventListenerSafe('exportEmployeeCustodyBtn', 'click', () => exportToExcel('employee-custody'));
}

// إعداد تبديل التبويبات
function setupTabSwitching() {
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.getAttribute('data-tab');
            
            // إزالة الفئة النشطة من جميع الأزرار والمحتويات
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.style.display = 'none');
            
            // إضافة الفئة النشطة للزر المحدد وإظهار المحتوى
            btn.classList.add('active');
            document.getElementById(targetTab).style.display = 'block';
            
            // تحديث البيانات حسب التبويب
            if (targetTab === 'deliver-custody') {
                loadAvailableCustodyForDelivery();
                displayDeliveryRecordsWithDataTables(); // إضافة تحديث جدول التسليم
            } else if (targetTab === 'undelivered-custody') {
                displayUndeliveredItems();
            }
        });
    });
}

// وظيفة لعرض تبويب محدد برمجيًا
function showTab(tabId) {
    // تحديث الأزرار النشطة
    tabBtns.forEach(btn => {
        if (btn.getAttribute('data-tab') === tabId) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
    
    // تحديث محتوى التبويبات
    tabContents.forEach(content => {
        if (content.id === tabId) {
            content.style.display = 'block';
            // تحديث البيانات حسب التبويب
            if (tabId === 'deliver-custody') {
                loadAvailableCustodyForDelivery();
                displayDeliveryRecordsWithDataTables(); // إضافة تحديث جدول التسليم
            } else if (tabId === 'undelivered-custody') {
                displayUndeliveredItems();
            }
        } else {
            content.style.display = 'none';
        }
    });
}

// معالجة إضافة عهدة
async function handleAddCustody(event) {
    event.preventDefault();
    
    const formData = {
        custody_code: document.getElementById('custodyCode').value,
        name: document.getElementById('custodyName').value,
        type: document.getElementById('custodyType').value,
        status: document.getElementById('custodyStatus').value,
        quantity: parseInt(document.getElementById('custodyQuantity').value),
        available_quantity: parseInt(document.getElementById('custodyQuantity').value)
    };
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(formData)
        });
        
        if (response.ok) {
            showNotification('تم إضافة العهدة بنجاح', 'success');
            document.getElementById('addCustodyForm').reset();
            await loadCustodyItems();
            calculateAvailableQuantities();
            await generateNextCustodyCode();
            displayCustodyItemsWithDataTables();
            displayUndeliveredItems();
            loadAvailableCustodyForDelivery();
        } else if (response.status === 409) {
            // حالة تكرار الاسم
            console.log('حالة تكرار - الكود 409');
            const error = await response.json();
            console.log('بيانات الخطأ:', error);
            if (error.duplicate_name) {
                console.log('تكرار اسم العهدة');
                showNotification(`تنبيه: ${error.error}`, 'warning');
                // تركيز على حقل اسم العهدة
                document.getElementById('custodyName').focus();
                document.getElementById('custodyName').select();
            } else {
                console.log('خطأ آخر في الكود 409');
                showNotification(error.error || 'خطأ في إضافة العهدة', 'error');
            }
        } else {
            console.log('خطأ آخر:', response.status);
            const error = await response.json();
            console.log('بيانات الخطأ:', error);
            showNotification(error.error || 'خطأ في إضافة العهدة', 'error');
        }
    } catch (error) {
        console.error('خطأ في إضافة العهدة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// دالة لعرض رسائل العهد - تم استبدالها بـ showNotification المحسنة
function showCustodyMessage(message, type) {
    console.log('🔄 تحويل showCustodyMessage إلى showNotification:', message, type);
    // تحويل نوع الرسالة إلى النوع المناسب للإشعارات المحسنة
    const notificationType = type === 'error' ? 'error' : type === 'success' ? 'success' : 'warning';
    showNotification(message, notificationType);
}

// معالجة تسليم عهدة
async function handleDeliverCustody(event) {
    event.preventDefault();
    
    // الحصول على اسم العهدة من الحقل المخفي
    const custodyCode = document.getElementById('custodyCodeDeliver').value;
    const custodyName = document.getElementById('custodyNameDeliver').value;
    
    const formData = {
        operation_number: document.getElementById('operationNumber').value,
        employee_code: document.getElementById('employeeCodeDeliver').value,
        department: document.getElementById('employeeDepartment').value,
        employee_name: document.getElementById('employeeName').value,
        custody_code: custodyCode,
        custody_name: custodyName,
        custody_type: document.getElementById('custodyTypeDeliver').value,
        quantity: parseInt(document.getElementById('deliveryQuantity').value),
        delivery_date: document.getElementById('deliveryDate').value,
        status: document.getElementById('deliveryStatus').value
    };

    // تسجيل البيانات المرسلة للتشخيص
    console.log('بيانات التسليم المرسلة:', formData);

    // تسجيل قيم الحقول الفردية للتشخيص
    console.log('قيم الحقول:');
    console.log('- رقم العملية:', document.getElementById('operationNumber').value);
    console.log('- كود الموظف:', document.getElementById('employeeCodeDeliver').value);
    console.log('- اسم الموظف:', document.getElementById('employeeName').value);
    console.log('- الإدارة:', document.getElementById('employeeDepartment').value);
    console.log('- كود العهدة:', custodyCode);
    console.log('- اسم العهدة:', custodyName);
    console.log('- نوع العهدة:', document.getElementById('custodyTypeDeliver').value);
    console.log('- الكمية:', document.getElementById('deliveryQuantity').value);
    console.log('- التاريخ:', document.getElementById('deliveryDate').value);
    console.log('- الحالة:', document.getElementById('deliveryStatus').value);

    // التحقق من صحة البيانات قبل الإرسال
    if (!formData.operation_number) {
        showNotification('رقم العملية مطلوب', 'warning');
        return;
    }
    if (!formData.employee_code) {
        showNotification('كود الموظف مطلوب', 'warning');
        return;
    }
    if (!formData.employee_name) {
        showNotification('اسم الموظف مطلوب', 'warning');
        return;
    }
    if (!formData.custody_code) {
        showNotification('كود العهدة مطلوب', 'warning');
        return;
    }
    if (!formData.custody_name) {
        showNotification('اسم العهدة مطلوب', 'warning');
        return;
    }
    if (!formData.custody_type) {
        showNotification('نوع العهدة مطلوب', 'warning');
        return;
    }
    if (!formData.quantity || formData.quantity <= 0) {
        showNotification('الكمية يجب أن تكون أكبر من صفر', 'warning');
        return;
    }
    if (!formData.delivery_date) {
        showNotification('تاريخ التسليم مطلوب', 'warning');
        return;
    }

    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody/delivery`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(formData)
        });
        
        if (response.ok) {
            showNotification('تم تسليم العهدة بنجاح', 'success');
            document.getElementById('deliverCustodyForm').reset();

            // إعادة تحميل البيانات وتحديث الجداول
            await loadDeliveryRecords();
            await loadCustodyItems();
            calculateAvailableQuantities();
            await generateNextOperationNumber();

            // تحديث الجداول مع تأخير قصير لضمان التحديث الصحيح
            setTimeout(() => {
                displayDeliveryRecordsWithDataTables();
                displayUndeliveredItems();
                loadAvailableCustodyForDelivery();
                console.log('تم تحديث جميع الجداول بعد التسليم');
            }, 100);
        } else {
            const error = await response.json();
            console.error('خطأ من الخادم:', error);
            console.error('حالة الاستجابة:', response.status);
            showNotification(error.error || 'خطأ في تسليم العهدة', 'error');
        }
    } catch (error) {
        console.error('خطأ في تسليم العهدة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}



// بحث عن موظف
function searchEmployee() {
    const searchValue = document.getElementById('employeeCodeDeliver').value.trim();
    const employee = employees.find(emp =>
        emp.code?.toString().includes(searchValue) ||
        SharedUtils.enhancedSearch(searchValue, emp.full_name || '')
    );

    if (employee) {
        document.getElementById('employeeCodeDeliver').value = employee.code;
        document.getElementById('employeeDepartment').value = employee.department || '';
        document.getElementById('employeeName').value = employee.full_name || '';
        console.log('تم العثور على الموظف');
    } else {
        document.getElementById('employeeName').value = '';
        showNotification('لم يتم العثور على الموظف', 'warning');
    }
}

// إعداد قائمة اقتراحات للبحث في تسليم العهد
function setupDeliverySearch() {
    const employeeCodeDeliver = document.getElementById('employeeCodeDeliver');
    const deliverySuggestions = document.getElementById('employee-delivery-suggestions');

    if (employeeCodeDeliver && deliverySuggestions) {
        employeeCodeDeliver.addEventListener('input', function() {
            const searchValue = this.value.trim();

            if (searchValue.length < 2) {
                deliverySuggestions.style.display = 'none';
                return;
            }

            const filteredEmployees = employees.filter(emp =>
                SharedUtils.enhancedSearch(searchValue, emp.full_name || '') ||
                emp.code?.toString().includes(searchValue)
            ).slice(0, 5);

            if (filteredEmployees.length > 0) {
                deliverySuggestions.innerHTML = filteredEmployees.map(emp =>
                    `<div class="suggestion-item" data-code="${emp.code}" data-name="${emp.full_name}">
                        <strong>${emp.code}</strong> - ${emp.full_name}
                    </div>`
                ).join('');
                deliverySuggestions.style.display = 'block';

                // إضافة مستمعي الأحداث للاقتراحات
                deliverySuggestions.querySelectorAll('.suggestion-item').forEach(item => {
                    item.addEventListener('click', function() {
                        const code = this.getAttribute('data-code');
                        const name = this.getAttribute('data-name');
                        employeeCodeDeliver.value = code;
                        document.getElementById('employeeName').value = name;

                        // البحث عن الموظف وملء باقي البيانات
                        const employee = employees.find(emp => emp.code == code);
                        if (employee) {
                            document.getElementById('employeeDepartment').value = employee.department || '';
                        }

                        deliverySuggestions.style.display = 'none';
                    });
                });
            } else {
                deliverySuggestions.style.display = 'none';
            }
        });

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!employeeCodeDeliver.contains(e.target) && !deliverySuggestions.contains(e.target)) {
                deliverySuggestions.style.display = 'none';
            }
        });
    }
}

// التعامل مع اختيار اسم الموظف من قائمة الاقتراحات
function handleEmployeeNameSelection() {
    const employeeCodeInput = document.getElementById('employeeCodeDeliver');
    const employeeNameInput = document.getElementById('employeeName');
    const employeeDepartmentInput = document.getElementById('employeeDepartment');
    const selectedValue = employeeCodeInput.value.trim();
    
    if (!selectedValue) {
        employeeNameInput.value = '';
        employeeDepartmentInput.value = '';
        return;
    }
    
    // البحث عن الموظف بالاسم الكامل
    const employee = employees.find(emp => 
        emp.full_name === selectedValue || emp.code === selectedValue
    );
    
    if (employee) {
        // إذا تم العثور على الموظف، املأ الحقول
        employeeCodeInput.value = employee.code;
        employeeNameInput.value = employee.full_name || '';
        employeeDepartmentInput.value = employee.department || '';
    } else {
        // البحث في خيارات datalist
        const datalist = document.getElementById('employeeNameSuggestions');
        if (datalist) {
            const options = datalist.querySelectorAll('option');
            for (const option of options) {
                if (option.value === selectedValue) {
                    const code = option.getAttribute('data-code');
                    if (code) {
                        // البحث عن الموظف بالكود
                        const employeeByCode = employees.find(emp => emp.code === code);
                        if (employeeByCode) {
                            employeeCodeInput.value = employeeByCode.code;
                            employeeNameInput.value = employeeByCode.full_name || '';
                            employeeDepartmentInput.value = employeeByCode.department || '';
                            return;
                        }
                    }
                }
            }
        }
    }
}

// استخدام دالة التطبيع المحسنة من shared-utils.js
function normalizeArabic(str) {
    return SharedUtils.normalizeArabicText(str);
}

// عرض اقتراحات الموظفين أثناء الكتابة
document.getElementById('employeeCodeDeliver').addEventListener('input', function() {
    const searchValue = this.value.trim();
    if (searchValue.length < 2) return;

    const suggestions = employees.filter(emp =>
        emp.code?.toString().includes(searchValue) ||
        SharedUtils.enhancedSearch(searchValue, emp.full_name || '')
    ).slice(0, 5);
    
    // إنشاء قائمة الاقتراحات
    let suggestionList = document.getElementById('employee-suggestions');
    if (!suggestionList) {
        suggestionList = document.createElement('div');
        suggestionList.id = 'employee-suggestions';
        suggestionList.className = 'suggestions-list';
        this.parentNode.appendChild(suggestionList);
    }
    
    // إضافة الاقتراحات للقائمة
    if (suggestions.length > 0) {
        suggestionList.innerHTML = '';
        suggestions.forEach(emp => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = `${emp.code} - ${emp.full_name}`;
            item.addEventListener('click', function() {
                document.getElementById('employeeCodeDeliver').value = emp.code;
                document.getElementById('employeeDepartment').value = emp.department || '';
                document.getElementById('employeeName').value = emp.full_name || '';
                suggestionList.innerHTML = '';
                suggestionList.style.display = 'none';
            });
            suggestionList.appendChild(item);
        });
        suggestionList.style.display = 'block';
    } else {
        suggestionList.innerHTML = '';
        suggestionList.style.display = 'none';
    }
});

// إخفاء قائمة الاقتراحات عند النقر خارجها
document.addEventListener('click', function(e) {
    const suggestionList = document.getElementById('employee-suggestions');
    if (suggestionList && !e.target.matches('#employeeCodeDeliver')) {
        suggestionList.innerHTML = '';
        suggestionList.style.display = 'none';
    }
});







// تحديث تفاصيل العهدة
function updateCustodyDetails() {
    const custodySelect = document.getElementById('custodyCodeDeliver');
    const custodyCode = custodySelect.value;

    if (custodyCode) {
        // البحث عن العهدة في المصفوفة للحصول على جميع البيانات
        const custody = custodyItems.find(item => item.custody_code == custodyCode);

        if (custody) {
            // استخدام البيانات من المصفوفة مباشرة
            const custodyName = custody.custody_name || custody.name || '';
            const custodyType = custody.custody_type || custody.type || '';

            document.getElementById('custodyTypeDeliver').value = custodyType;
            document.getElementById('custodyNameDeliver').value = custodyName;
            document.getElementById('deliveryQuantity').max = custody.available_quantity;
            document.getElementById('deliveryQuantity').value = Math.min(1, custody.available_quantity);

            console.log('تم تحديث تفاصيل العهدة:', {
                code: custodyCode,
                name: custodyName,
                type: custodyType,
                available: custody.available_quantity
            });

            // تأكيد أن القيم تم تعيينها بشكل صحيح
            console.log('قيم الحقول بعد التحديث:');
            console.log('- نوع العهدة في الحقل:', document.getElementById('custodyTypeDeliver').value);
            console.log('- اسم العهدة في الحقل:', document.getElementById('custodyNameDeliver').value);
        }
    } else {
        // مسح القيم عند عدم اختيار عهدة
        document.getElementById('custodyTypeDeliver').value = '';
        document.getElementById('custodyNameDeliver').value = '';
        document.getElementById('deliveryQuantity').max = '';
        document.getElementById('deliveryQuantity').value = '';

        console.log('تم مسح تفاصيل العهدة');
    }
}

// تحميل العهد المتاحة للتسليم
function loadAvailableCustodyForDelivery(searchTerm = '') {
    const custodySelect = document.getElementById('custodyCodeDeliver');
    custodySelect.innerHTML = '<option value="">اختر العهدة</option>';
    
    let availableCustody = custodyItems.filter(item => item.available_quantity > 0);
    
    // تصفية العهد بناءً على مصطلح البحث إذا كان موجودًا
    if (searchTerm) {
        availableCustody = availableCustody.filter(custody => {
            const custodyName = custody.custody_name || custody.name || '';
            const custodyType = custody.custody_type || custody.type || '';

            return SharedUtils.enhancedSearch(searchTerm, custodyName) ||
                   custody.custody_code?.toString().includes(searchTerm) ||
                   SharedUtils.enhancedSearch(searchTerm, custodyType);
        });
    }
    
    availableCustody.forEach(custody => {
        const option = document.createElement('option');
        option.value = custody.custody_code;

        // استخدام الأسماء الصحيحة للحقول
        const custodyName = custody.custody_name || custody.name || '';
        const custodyType = custody.custody_type || custody.type || '';

        option.setAttribute('data-custody-name', custodyName);
        option.setAttribute('data-custody-type', custodyType);
        option.textContent = `${custodyName} - ${custodyType} (متاح: ${custody.available_quantity})`;
        custodySelect.appendChild(option);
    });
}

// تطبيع الحروف العربية للبحث


// البحث عن الموظف لعرض تقرير العهدة
function searchEmployeeForReport() {
    const searchTerm = document.getElementById('employeeReportSearch').value.trim();
    if (!searchTerm) {
        // إخفاء معلومات الموظف والجدول عند مسح حقل البحث
        document.getElementById('employeeInfoContainer').style.display = 'none';
        document.getElementById('employeeCustodyTableBody').innerHTML = '';
        return;
    }
    
    // البحث عن الموظف
    const employee = employees.find(emp =>
        SharedUtils.enhancedSearch(searchTerm, emp.full_name || '') ||
        emp.code?.toString().includes(searchTerm)
    );
    
    if (!employee) {
        // إخفاء معلومات الموظف والجدول عند عدم العثور على الموظف
        document.getElementById('employeeInfoContainer').style.display = 'none';
        document.getElementById('employeeCustodyTableBody').innerHTML = '';
        return;
    }
    
    // عرض معلومات الموظف
    document.getElementById('reportEmployeeCode').textContent = employee.code;
    document.getElementById('reportEmployeeName').textContent = employee.full_name || 'غير محدد';
    document.getElementById('reportEmployeeDepartment').textContent = employee.department || 'غير محدد';
    
    // إظهار حاوية معلومات الموظف
    document.getElementById('employeeInfoContainer').style.display = 'block';
    
    // البحث عن عهد الموظف
    const employeeDeliveries = deliveryRecords.filter(record => 
        record.employee_code.toString() === employee.code.toString()
    );
    
    if (employeeDeliveries.length === 0) {
        document.getElementById('employeeCustodyTableBody').innerHTML =
            '<tr><td colspan="8" class="no-data">لا توجد عهد مسجلة</td></tr>';
        return;
    }
    
    // عرض عهد الموظف
    const tbody = document.getElementById('employeeCustodyTableBody');
    tbody.innerHTML = '';
    
    employeeDeliveries.forEach(delivery => {
        // البحث عن معلومات العهدة
        const custodyItem = custodyItems.find(item => item.custody_code === delivery.custody_code);
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${delivery.custody_code || 'غير محدد'}</td>
            <td>${custodyItem ? custodyItem.name : delivery.custody_name || 'غير محدد'}</td>
            <td>${delivery.custody_type || 'غير محدد'}</td>
            <td>${new Date(delivery.delivery_date).toLocaleDateString('ar-EG')}</td>
            <td><strong>${delivery.quantity}</strong></td>
        `;
        tbody.appendChild(row);
    });
    
    // تطبيق الصلاحيات على أزرار الجدول
    setupDeliveryActionButtons();
    
    // عرض قسم التقرير
    document.getElementById('employee-custody-report').style.display = 'block';
    document.querySelector('[data-tab="employee-custody-report"]').classList.add('active');
}

// طباعة تقرير عهدة الموظف
function printEmployeeCustodyReport() {
    const employeeCode = document.getElementById('employeeReportCode').textContent;
    const employeeName = document.getElementById('employeeReportName').textContent;
    const employeeDepartment = document.getElementById('employeeReportDepartment').textContent;
    
    if (!employeeCode || !employeeName) {
        showNotification('يرجى البحث عن موظف أولاً', 'warning');
        return;
    }
    
    const custodyTable = document.getElementById('employeeCustodyTable');
    if (!custodyTable) return;
    
    const printWindow = window.open('', '', 'width=800,height=600');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تقرير عهدة الموظف - ${employeeName}</title>
            <style>
                @page { size: A4; margin: 1cm; }
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                h1 { text-align: center; color: #333; margin-bottom: 20px; }
                h3 { margin-top: 20px; color: #555; border-bottom: 2px solid #2196F3; padding-bottom: 5px; }
                .info-container { display: flex; justify-content: space-between; margin-bottom: 20px; background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
                .info-item { padding: 10px; }
                .info-label { font-weight: bold; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                th, td { border: 1px solid #ddd; padding: 10px; text-align: right; }
                th { background-color: #2196F3; color: white; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .status-delivered { color: #2196F3; font-weight: bold; }
                .status-returned { color: #4CAF50; font-weight: bold; }
                .status-partial { color: #FF9800; font-weight: bold; }
                .btn { display: inline-block; padding: 8px 20px; border-radius: 4px; cursor: pointer; font-weight: bold; text-align: center; margin: 0 10px; min-width: 100px; border: none; }
                .print-btn { background-color: #2196F3; color: white; }
                .close-btn { background-color: #f44336; color: white; }
                @media print { 
                    .no-print { display: none; } 
                    body { margin: 0; padding: 10px; }
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; page-break-after: auto; }
                    h1 { margin-top: 0; }
                }
            </style>
        </head>
        <body>
            <h1>تقرير عهدة الموظف</h1>
            
            <div class="info-container">
                <div class="info-item"><span class="info-label">كود الموظف:</span> ${employeeCode}</div>
                <div class="info-item"><span class="info-label">اسم الموظف:</span> ${employeeName}</div>
                <div class="info-item"><span class="info-label">القسم:</span> ${employeeDepartment}</div>
            </div>
            
            <h3>قائمة العهد المستلمة</h3>
            ${custodyTable.outerHTML}
            
            <div class="no-print" style="margin-top: 30px; text-align: center;">
                <button onclick="window.print()" class="btn print-btn">طباعة</button>
                <button onclick="window.close()" class="btn close-btn">إغلاق</button>
            </div>
            
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                    }, 500);
                }
            </script>
        </body>
        </html>
    `);
    printWindow.document.close();
}

// وظيفة البحث في العهد للتسليم
function searchCustody() {
    const searchTerm = document.getElementById('searchCustodyDeliver').value.trim();
    loadAvailableCustodyForDelivery(searchTerm);
}

// عرض العهد
function displayCustodyItems() {
    const tbody = document.getElementById('custodyTableBody');
    tbody.innerHTML = '';

    // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
    const sortedItems = [...custodyItems].sort((a, b) => {
      // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
      const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
      const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
      return bTime - aTime; // الأحدث أولاً
    });

    sortedItems.forEach((item, index) => {
        // حساب الكمية المصروفة بشكل صحيح
        // نحسب الكميات المسلمة حاليًا (بحالة "مسلم")
        const currentlyDelivered = deliveryRecords
            .filter(rec => rec.custody_code == item.custody_code && rec.status === 'مسلم')
            .reduce((sum, rec) => sum + (parseInt(rec.quantity) || 0), 0);
        
        // لا توجد كميات مسترجعة بعد حذف قسم الاسترجاع
        const returned = 0;
        
        // الكمية المصروفة = الكميات المسلمة حاليًا
        // لأن سجلات التسليم بحالة "مسلم" تعكس بالفعل الكميات المتبقية بعد الاسترجاع
        const spentQuantity = currentlyDelivered;
        const remainingQuantity = item.quantity - spentQuantity;
        
        const row = document.createElement('tr');

        // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
        if (index === 0) {
          row.style.backgroundColor = '#e8f5e8';
          row.style.border = '2px solid #4CAF50';
        }

        row.innerHTML = `
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.custody_code}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.name}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.type}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.status}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.quantity}</td>
            <td class="quantity-spent" style="${index === 0 ? 'font-weight: bold;' : ''}">${spentQuantity}</td>
            <td class="quantity-remaining" style="${index === 0 ? 'font-weight: bold;' : ''}">${remainingQuantity}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.created_at ? new Date(item.created_at).toLocaleDateString('ar-EG') : ''}</td>
            <td>
                <button class="action-btn details-btn" onclick="showCustodyDetails(${item.id})">تفاصيل المستلم</button>
                ${hasPermission('edit_custody') ? `<button class="action-btn edit-btn" onclick="editCustody(${item.id})">تعديل</button>` : ''}
                ${hasPermission('delete_custody') ? `<button class="action-btn delete-btn" onclick="confirmDeleteCustody(${item.id})">حذف</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
    
    // الأزرار الآن تُخفى مباشرة في HTML
 }

// عرض سجلات التسليم
function displayDeliveryRecords() {
    const tbody = document.getElementById('deliveryTableBody');
    if (!tbody) {
        console.error('لم يتم العثور على جدول التسليم');
        return;
    }

    tbody.innerHTML = '';
    console.log('عرض سجلات التسليم:', deliveryRecords.length, 'سجل');

    if (deliveryRecords.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="11" class="no-results">لا توجد سجلات تسليم</td>`;
        tbody.appendChild(row);
        return;
    }

    // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
    const sortedRecords = [...deliveryRecords].sort((a, b) => {
      // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
      const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
      const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
      return bTime - aTime; // الأحدث أولاً
    });

    sortedRecords.forEach((record, index) => {
        const row = document.createElement('tr');

        // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
        if (index === 0) {
          row.style.backgroundColor = '#e8f5e8';
          row.style.border = '2px solid #4CAF50';
        }

        row.innerHTML = `
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.operation_number}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.employee_code}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.employee_name}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.department}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.custody_code}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.custody_name || getCustodyName(record.custody_code)}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.custody_type}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.quantity}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${new Date(record.delivery_date).toLocaleDateString('ar-EG')}</td>
            <td><span class="status-${record.status === 'مسلم' ? 'delivered' : 'pending'}" style="${index === 0 ? 'font-weight: bold;' : ''}">${record.status}</span></td>
            <td>
                ${hasPermission('edit_deliver_custody') ? `<button class="action-btn edit-btn" onclick="editDelivery(${record.id})">تعديل</button>` : ''}
                ${hasPermission('delete_deliver_custody') ? `<button class="action-btn delete-btn" onclick="confirmDeleteDelivery(${record.id})">حذف</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
    
    // تطبيق الصلاحيات على أزرار الجدول
    setupDeliveryActionButtons();
}

// الحصول على اسم العهدة من كود العهدة
function getCustodyName(custodyCode) {
    const custody = custodyItems.find(item => item.custody_code == custodyCode);
    return custody ? custody.name : '-';
}

// عرض العهد غير المسلمة
function displayUndeliveredItems() {
    const tbody = document.getElementById('undeliveredTableBody');
    tbody.innerHTML = '';
    
    const undeliveredItems = custodyItems.filter(item => item.available_quantity > 0);
    
    // تحديث عدد العهد غير المسلمة
    updateCustodyCount(undeliveredItems.length);
    
    // تطبيق التصفية إذا كانت موجودة
    const filteredItems = filterUndeliveredItems(undeliveredItems);
    
    if (filteredItems.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="6" class="no-results">لا توجد عهد غير مسلمة</td>`;
        tbody.appendChild(row);
        return;
    }
    
    filteredItems.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.custody_code}</td>
            <td>${item.name}</td>
            <td>${item.type}</td>
            <td>${item.status}</td>
            <td>${item.available_quantity}</td>
            <td>${item.created_at ? new Date(item.created_at).toLocaleDateString('ar-EG') : ''}</td>
        `;
        tbody.appendChild(row);
    });
}

// تصفية العهد غير المسلمة
function filterUndeliveredItems(items) {
    const searchTerm = document.getElementById('searchUndeliveredAll')?.value.toLowerCase() || '';
    
    // إذا لم تكن هناك معايير بحث، أعد جميع العناصر
    if (!searchTerm) {
        return items;
    }
    
    return items.filter(item => {
        // البحث بكود العهدة
        const codeMatch = item.custody_code.toString().includes(searchTerm);
        
        // البحث باسم العهدة
        const nameMatch = item.name.toLowerCase().includes(searchTerm);
        
        // البحث بنوع العهدة
        const typeMatch = item.type.toLowerCase().includes(searchTerm);
        
        // إذا تطابق أي من المعايير، أعد العنصر
        return codeMatch || nameMatch || typeMatch;
    });
}

// تحديث عدد العهد
function updateCustodyCount(count) {
    const title = document.querySelector('#undelivered-custody h2');
    if (title) {
        title.textContent = `العهد غير المسلمة (${count})`;
    }
}



// دالة إظهار الإشعارات المحسنة
function showNotification(message, type = 'info') {
  console.log(`📢 عرض إشعار: ${message} (نوع: ${type})`);

  // إزالة الإشعارات السابقة
  const existingNotifications = document.querySelectorAll('.enhanced-notification');
  existingNotifications.forEach(notif => {
    if (notif.parentNode) {
      notif.parentNode.removeChild(notif);
    }
  });

  // إنشاء حاوي الإشعار
  const notificationContainer = document.createElement('div');
  notificationContainer.className = `enhanced-notification notification-${type}`;

  // تحديد الأيقونة والألوان حسب النوع
  let icon, bgColor, borderColor, textColor;
  switch(type) {
    case 'error':
      icon = '❌';
      bgColor = 'linear-gradient(135deg, #ff6b6b, #ee5a52)';
      borderColor = '#ff4757';
      textColor = '#ffffff';
      break;
    case 'success':
      icon = '✅';
      bgColor = 'linear-gradient(135deg, #28a745, #20c997)';
      borderColor = '#28a745';
      textColor = '#ffffff';
      break;
    case 'warning':
      icon = '⚠️';
      bgColor = 'linear-gradient(135deg, #ffa502, #ff6348)';
      borderColor = '#ffa502';
      textColor = '#ffffff';
      break;
    default:
      icon = 'ℹ️';
      bgColor = 'linear-gradient(135deg, #3742fa, #2f3542)';
      borderColor = '#3742fa';
      textColor = '#ffffff';
  }

  // تنسيق الحاوي
  notificationContainer.style.cssText = `
    position: fixed;
    top: -200px;
    left: 50%;
    transform: translateX(-50%);
    min-width: 350px;
    max-width: 500px;
    background: ${bgColor};
    border: 2px solid ${borderColor};
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    backdrop-filter: blur(10px);
  `;

  // إنشاء المحتوى
  const content = document.createElement('div');
  content.style.cssText = `
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
  `;

  // إنشاء الأيقونة
  const iconElement = document.createElement('div');
  iconElement.style.cssText = `
    font-size: 2rem;
    flex-shrink: 0;
    animation: pulse 2s infinite;
  `;
  iconElement.textContent = icon;

  // إنشاء النص
  const messageElement = document.createElement('div');
  messageElement.style.cssText = `
    color: ${textColor};
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.4;
    flex: 1;
  `;
  messageElement.textContent = message;

  // إنشاء زر الإغلاق
  const closeButton = document.createElement('button');
  closeButton.style.cssText = `
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: ${textColor};
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
  `;
  closeButton.innerHTML = '×';
  closeButton.title = 'إغلاق';

  // تأثير التحويم على زر الإغلاق
  closeButton.addEventListener('mouseenter', () => {
    closeButton.style.background = 'rgba(255, 255, 255, 0.3)';
    closeButton.style.transform = 'scale(1.1)';
  });

  closeButton.addEventListener('mouseleave', () => {
    closeButton.style.background = 'rgba(255, 255, 255, 0.2)';
    closeButton.style.transform = 'scale(1)';
  });

  // إنشاء رسالة التأكيد
  const confirmMessage = document.createElement('div');
  confirmMessage.style.cssText = `
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    text-align: center;
    color: ${textColor};
    font-size: 0.85rem;
    cursor: pointer;
    transition: background 0.3s ease;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  `;
  confirmMessage.textContent = 'موافق';

  // تأثير التحويم على رسالة التأكيد
  confirmMessage.addEventListener('mouseenter', () => {
    confirmMessage.style.background = 'rgba(255, 255, 255, 0.2)';
  });

  confirmMessage.addEventListener('mouseleave', () => {
    confirmMessage.style.background = 'rgba(255, 255, 255, 0.1)';
  });

  // تجميع العناصر
  content.appendChild(iconElement);
  content.appendChild(messageElement);
  content.appendChild(closeButton);
  notificationContainer.appendChild(content);
  notificationContainer.appendChild(confirmMessage);

  // إضافة CSS للأنيميشن
  const style = document.createElement('style');
  style.textContent = `
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }

    .enhanced-notification.removing {
      transform: translateX(-50%) translateY(-200px) scale(0.8);
      opacity: 0;
    }
  `;
  if (!document.querySelector('#enhanced-notification-styles-custody')) {
    style.id = 'enhanced-notification-styles-custody';
    document.head.appendChild(style);
  }

  // دالة إزالة الإشعار
  const removeNotification = () => {
    if (notificationContainer.parentNode) {
      notificationContainer.classList.add('removing');
      setTimeout(() => {
        if (notificationContainer.parentNode) {
          notificationContainer.parentNode.removeChild(notificationContainer);
          console.log('🗑️ تم إزالة الإشعار');
        }
      }, 400);
    }
  };

  // إضافة مستمعي الأحداث للإغلاق
  closeButton.addEventListener('click', (e) => {
    e.stopPropagation();
    removeNotification();
  });

  confirmMessage.addEventListener('click', () => {
    removeNotification();
  });

  // إضافة الإشعار للصفحة
  document.body.appendChild(notificationContainer);

  // تشغيل أنيميشن الدخول
  setTimeout(() => {
    notificationContainer.style.top = '30px';
  }, 100);

  // تم إزالة الاختفاء التلقائي - الإشعار يبقى حتى يتم إغلاقه يدوياً
}

// دالة إظهار إشعار التأكيد للحذف
function showConfirmationNotification(title, message, onConfirm) {
  console.log('🔔 عرض إشعار تأكيد:', title);

  // إزالة أي إشعارات تأكيد سابقة
  const existingConfirmations = document.querySelectorAll('.enhanced-notification.notification-error');
  existingConfirmations.forEach(notif => {
    if (notif.parentNode) {
      notif.parentNode.removeChild(notif);
    }
  });

  // إنشاء حاوي الإشعار
  const notificationContainer = document.createElement('div');
  notificationContainer.className = 'enhanced-notification notification-error';

  // تنسيق الحاوي
  notificationContainer.style.cssText = `
    position: fixed;
    top: -250px;
    left: 50%;
    transform: translateX(-50%);
    min-width: 400px;
    max-width: 550px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border: 2px solid #ff4757;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    backdrop-filter: blur(10px);
  `;

  // إنشاء المحتوى الرئيسي
  const content = document.createElement('div');
  content.style.cssText = `
    padding: 25px;
    text-align: center;
  `;

  // إنشاء الأيقونة
  const iconElement = document.createElement('div');
  iconElement.style.cssText = `
    font-size: 3rem;
    margin-bottom: 15px;
    animation: shake 0.5s ease-in-out;
  `;
  iconElement.textContent = '🗑️';

  // إنشاء العنوان
  const titleElement = document.createElement('h3');
  titleElement.style.cssText = `
    color: #ffffff;
    margin: 0 0 10px 0;
    font-size: 1.3rem;
    font-weight: 600;
  `;
  titleElement.textContent = title;

  // إنشاء الرسالة
  const messageElement = document.createElement('p');
  messageElement.style.cssText = `
    color: #ffffff;
    margin: 0 0 25px 0;
    font-size: 1rem;
    line-height: 1.5;
    opacity: 0.9;
  `;
  messageElement.textContent = message;

  // إنشاء حاوي الأزرار
  const buttonsContainer = document.createElement('div');
  buttonsContainer.style.cssText = `
    display: flex;
    gap: 15px;
    justify-content: center;
  `;

  // زر التأكيد
  const confirmButton = document.createElement('button');
  confirmButton.style.cssText = `
    background: #ffffff;
    color: #ff4757;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
  `;
  confirmButton.textContent = 'تأكيد الحذف';

  // زر الإلغاء
  const cancelButton = document.createElement('button');
  cancelButton.style.cssText = `
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border: 2px solid #ffffff;
    padding: 10px 25px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
  `;
  cancelButton.textContent = 'إلغاء';

  // تأثيرات التحويم
  confirmButton.addEventListener('mouseenter', () => {
    confirmButton.style.background = '#f8f9fa';
    confirmButton.style.transform = 'translateY(-2px)';
    confirmButton.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
  });

  confirmButton.addEventListener('mouseleave', () => {
    confirmButton.style.background = '#ffffff';
    confirmButton.style.transform = 'translateY(0)';
    confirmButton.style.boxShadow = 'none';
  });

  cancelButton.addEventListener('mouseenter', () => {
    cancelButton.style.background = 'rgba(255, 255, 255, 0.3)';
    cancelButton.style.transform = 'translateY(-2px)';
  });

  cancelButton.addEventListener('mouseleave', () => {
    cancelButton.style.background = 'rgba(255, 255, 255, 0.2)';
    cancelButton.style.transform = 'translateY(0)';
  });

  // دالة إزالة الإشعار
  const removeConfirmation = () => {
    if (notificationContainer.parentNode) {
      notificationContainer.style.transform = 'translateX(-50%) translateY(-200px) scale(0.8)';
      notificationContainer.style.opacity = '0';
      setTimeout(() => {
        if (notificationContainer.parentNode) {
          notificationContainer.parentNode.removeChild(notificationContainer);
          console.log('🗑️ تم إزالة إشعار التأكيد');
        }
      }, 400);
    }
  };

  // إضافة مستمعي الأحداث
  confirmButton.addEventListener('click', () => {
    console.log('✅ تم تأكيد الحذف');
    removeConfirmation();
    if (typeof onConfirm === 'function') {
      onConfirm();
    }
  });

  cancelButton.addEventListener('click', () => {
    console.log('❌ تم إلغاء الحذف');
    removeConfirmation();
  });

  // تجميع العناصر
  buttonsContainer.appendChild(confirmButton);
  buttonsContainer.appendChild(cancelButton);

  content.appendChild(iconElement);
  content.appendChild(titleElement);
  content.appendChild(messageElement);
  content.appendChild(buttonsContainer);

  notificationContainer.appendChild(content);

  // إضافة CSS للأنيميشن
  const style = document.createElement('style');
  style.textContent = `
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }
  `;
  if (!document.querySelector('#confirmation-notification-styles-custody')) {
    style.id = 'confirmation-notification-styles-custody';
    document.head.appendChild(style);
  }

  // إضافة الإشعار للصفحة
  document.body.appendChild(notificationContainer);

  // تشغيل أنيميشن الدخول
  setTimeout(() => {
    notificationContainer.style.top = '50px';
  }, 100);
}

// البحث في العهد من السيرفر
async function searchCustodyFromServer() {
    try {
        const searchInput = document.getElementById('searchCustody');
        if (!searchInput) {
            console.error('حقل البحث غير موجود');
            return;
        }

        const searchTerm = searchInput.value.trim();

        const params = new URLSearchParams();
        if (searchTerm) {
            params.append('name', searchTerm);
            params.append('type', searchTerm);
            params.append('custody_code', searchTerm);
        }

        let url = `${API_URL}/api/custody`;
        if (params.toString()) {
            url = `${API_URL}/api/custody/search?${params.toString()}`;
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب بيانات العهد');
        }

        const filteredItems = await response.json();
        custodyItems = filteredItems;
        displayCustodyItemsWithDataTables();

    } catch (error) {
        console.error('خطأ في البحث:', error);
        showNotification('خطأ في البحث عن العهد', 'error');
    }
}

// تصفية العهد (استخدام البحث من السيرفر)
function filterCustodyItems() {
    searchCustodyFromServer();
}



// البحث في سجلات التسليم من السيرفر
async function searchDeliveryFromServer() {
    try {
        const searchTerm = document.getElementById('searchDelivery').value.toLowerCase();

        const params = new URLSearchParams();
        if (searchTerm) {
            params.append('employee_name', searchTerm);
            params.append('custody_type', searchTerm);
            params.append('employee_code', searchTerm);
        }

        let url = `${API_URL}/api/custody/delivery`;
        if (params.toString()) {
            url = `${API_URL}/api/custody/delivery/search?${params.toString()}`;
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب بيانات سجلات التسليم');
        }

        const filteredRecords = await response.json();
        deliveryRecords = filteredRecords;
        displayDeliveryRecords(filteredRecords);

    } catch (error) {
        console.error('خطأ في البحث:', error);
        showNotification('خطأ في البحث عن سجلات التسليم', 'error');
    }
}

// تصفية سجلات التسليم (استخدام البحث من السيرفر)
function filterDeliveryRecords() {
    searchDeliveryFromServer();
}

// عرض سجلات التسليم
function displayDeliveryRecords(records) {
    const tbody = document.getElementById('deliveryTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    // التحقق من وجود البيانات
    if (!records || !Array.isArray(records)) {
        tbody.innerHTML = '<tr><td colspan="11" class="no-data" style="text-align: center; padding: 20px; color: #666;">لا توجد سجلات تسليم</td></tr>';
        return;
    }

    records.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.operation_number}</td>
            <td>${record.employee_code}</td>
            <td>${record.employee_name}</td>
            <td>${record.department}</td>
            <td>${record.custody_code}</td>
            <td>${record.custody_name || getCustodyName(record.custody_code)}</td>
            <td>${record.custody_type}</td>
            <td>${record.quantity}</td>
            <td>${new Date(record.delivery_date).toLocaleDateString('ar-EG')}</td>
            <td><span class="status-${record.status === 'مسلم' ? 'delivered' : 'pending'}">${record.status}</span></td>
            <td>
                <button class="action-btn edit-btn" data-permission="can_edit" onclick="editDelivery(${record.id})">تعديل</button>
                <button class="action-btn delete-btn" data-permission="can_delete" onclick="deleteDelivery(${record.id})">حذف</button>
            </td>
        `;
        tbody.appendChild(row);
    });

    // تطبيق الصلاحيات على أزرار الجدول
    setupDeliveryActionButtons();
}

// إعداد البحث في العهد غير المسلمة
function setupUndeliveredSearch() {
    // إضافة مستمع الأحداث لحقل البحث الموحد
    const searchField = document.getElementById('searchUndeliveredAll');
    if (searchField) {
        searchField.addEventListener('input', () => {
            displayUndeliveredItems();
        });
    }
    
    // إضافة مستمع حدث لزر التحديث
    const refreshBtn = document.getElementById('refreshUndeliveredBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', async () => {
            await loadCustodyItems();
            calculateAvailableQuantities();
            displayUndeliveredItems();
            loadAvailableCustodyForDelivery();
            console.log('تم تحديث العهد غير المسلمة');
        });
    }
}

// تحميل الأقسام
function loadDepartments() {
    const departmentFilter = document.getElementById('departmentFilter');
    if (!departmentFilter) return; // إذا لم يوجد العنصر لا تفعل شيئًا
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))];
    departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
    departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentFilter.appendChild(option);
    });
}

// إعداد مستمعي أحداث التقارير
function setupReportEventListeners() {
    // تقرير العهد حسب الموظف
    document.getElementById('generateEmployeeReportBtn').addEventListener('click', generateEmployeeReport);
    document.getElementById('exportEmployeeReportBtn').addEventListener('click', () => exportToExcel('employee-report'));
    
    // تقرير العهد المسترجعة
    document.getElementById('generateReturnedReportBtn').addEventListener('click', generateReturnedReport);
    document.getElementById('exportReturnedReportBtn').addEventListener('click', () => exportToExcel('returned-report'));
    
    // تقرير العهد غير المسترجعة
    document.getElementById('generatePendingReportBtn').addEventListener('click', generatePendingReport);
    document.getElementById('exportPendingReportBtn').addEventListener('click', () => exportToExcel('pending-report'));
    
    // تقرير حسب نوع العهدة
    document.getElementById('generateTypeReportBtn').addEventListener('click', generateTypeReport);
    document.getElementById('exportTypeReportBtn').addEventListener('click', () => exportToExcel('type-report'));
    
    // تقرير حسب القسم
    document.getElementById('generateDepartmentReportBtn').addEventListener('click', generateDepartmentReport);
    document.getElementById('exportDepartmentReportBtn').addEventListener('click', () => exportToExcel('department-report'));
}

// إنشاء تقرير العهد حسب الموظف
function generateEmployeeReport() {
    const searchTerm = document.getElementById('employeeReportSearch').value.toLowerCase();
    const tbody = document.getElementById('employeeReportBody');
    tbody.innerHTML = '';
    
    let filteredDeliveries = deliveryRecords;
    if (searchTerm) {
        filteredDeliveries = deliveryRecords.filter(record => 
            (record.employee_name && record.employee_name.toLowerCase().includes(searchTerm)) ||
            (record.employee_code && record.employee_code.toString().includes(searchTerm))
        );
    }
    
    // تجميع العهد حسب الموظف
    const employeeDeliveries = {};
    
    filteredDeliveries.forEach(record => {
        if (!employeeDeliveries[record.employee_code]) {
            employeeDeliveries[record.employee_code] = {
                name: record.employee_name,
                department: record.department || 'غير محدد',
                deliveries: []
            };
        }
        employeeDeliveries[record.employee_code].deliveries.push(record);
    });
    
    // عرض العهد لكل موظف
    Object.keys(employeeDeliveries).forEach(empCode => {
        const employee = employeeDeliveries[empCode];
        
        employee.deliveries.forEach(record => {
            const status = 'مسلمة';
            const returnDate = '-';
            const custodyItem = custodyItems.find(item => item.custody_code === record.custody_code);
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${record.employee_code}</td>
                <td>${record.employee_name}</td>
                <td>${employee.department}</td>
                <td>${custodyItem ? custodyItem.name : record.custody_type || 'غير محدد'}</td>
                <td>${record.quantity}</td>
                <td>${new Date(record.delivery_date).toLocaleDateString('ar-EG')}</td>
                <td><span class="status-${status === 'مسترجعة' ? 'returned' : 'delivered'}">${status}</span></td>
                <td>${returnDate}</td>
            `;
            tbody.appendChild(row);
        });
    });
    
    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="no-data">لا توجد بيانات متاحة</td></tr>';
    }
}



// إنشاء تقرير العهد المسلمة
function generatePendingReport() {
    const tbody = document.getElementById('pendingReportBody');
    tbody.innerHTML = '';
    
    // الحصول على جميع العهد المسلمة (بما أنه لا يوجد استرجاع)
    const pendingDeliveries = deliveryRecords.filter(record => record.status === 'مسلم');
    
    // تجميع البيانات حسب الموظف والعهدة
    pendingDeliveries.forEach(record => {
        // البحث عن معلومات العهدة
        const custodyItem = custodyItems.find(item => item.custody_code === record.custody_code);
        
        // حساب المدة منذ التسليم
        const deliveryDate = new Date(record.delivery_date);
        const today = new Date();
        const daysDiff = Math.floor((today - deliveryDate) / (1000 * 60 * 60 * 24));
        
        // الكمية المتبقية هي نفس الكمية المسلمة (بما أنه لا يوجد استرجاع)
        const remainingQuantity = record.quantity;
        
        // البحث عن معلومات القسم
        const employee = employees.find(emp => emp.code === record.employee_code);
        const department = employee ? employee.department : 'غير محدد';
        
        // تحديد حالة العهدة بناءً على المدة
        let statusClass = 'normal';
        let statusText = 'عادية';
        
        if (daysDiff > 180) { // أكثر من 6 أشهر
            statusClass = 'critical';
            statusText = 'متأخرة جداً';
        } else if (daysDiff > 90) { // أكثر من 3 أشهر
            statusClass = 'warning';
            statusText = 'متأخرة';
        }
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.employee_code}</td>
            <td>${record.employee_name}</td>
            <td>${department}</td>
            <td>${custodyItem ? custodyItem.name : record.custody_type || 'غير محدد'}</td>
            <td>${record.quantity}</td>
            <td>${returnedQuantity}</td>
            <td>${remainingQuantity}</td>
            <td>${deliveryDate.toLocaleDateString('ar-EG')}</td>
            <td>${daysDiff} يوم</td>
            <td><span class="status-${statusClass}">${statusText}</span></td>
        `;
        tbody.appendChild(row);
    });
    
    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="no-data">لا توجد عهد معلقة</td></tr>';
    }
}

// إنشاء تقرير حسب نوع العهدة
function generateTypeReport() {
    const selectedType = document.getElementById('custodyTypeFilter').value;
    const tbody = document.getElementById('typeReportBody');
    tbody.innerHTML = '';
    
    // الحصول على أنواع العهد المتاحة
    let custodyTypes = [...new Set(custodyItems.map(item => item.type).filter(Boolean))];
    if (selectedType) {
        custodyTypes = [selectedType];
    }
    
    // تجميع البيانات حسب نوع العهدة
    custodyTypes.forEach(type => {
        // الحصول على العهد من هذا النوع
        const typeItems = custodyItems.filter(item => item.type === type);
        
        // حساب إجمالي الكميات المتاحة
        const totalQuantity = typeItems.reduce((sum, item) => sum + item.quantity, 0);
        const availableQuantity = typeItems.reduce((sum, item) => sum + (item.available_quantity || 0), 0);
        
        // الحصول على سجلات التسليم لهذا النوع
        const typeDeliveries = deliveryRecords.filter(record => {
            const custodyItem = custodyItems.find(item => item.custody_code === record.custody_code);
            return custodyItem && custodyItem.type === type;
        });
        
        // بعد حذف قسم الاسترجاع، لا توجد كميات مسترجعة
        const totalDelivered = typeDeliveries.reduce((sum, record) => sum + record.quantity, 0);
        const totalReturned = 0;
        const damagedItems = 0;
        const lostItems = 0;

        // جميع الكميات المسلمة تبقى مع الموظفين
        const remainingWithEmployees = totalDelivered;
        
        // الحصول على قائمة الموظفين الذين لديهم عهد من هذا النوع
        // بعد حذف قسم الاسترجاع، جميع العهد المسلمة تبقى مع الموظفين
        const employeesWithCustody = typeDeliveries
            .filter(delivery => delivery.status === 'مسلم')
            .map(delivery => delivery.employee_name);
        
        const uniqueEmployees = [...new Set(employeesWithCustody)];
        const employeesList = uniqueEmployees.join(', ');
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${type}</td>
            <td>${totalQuantity}</td>
            <td>${availableQuantity}</td>
            <td>${remainingWithEmployees}</td>
            <td>${damagedItems}</td>
            <td>${lostItems}</td>
            <td>${uniqueEmployees.length}</td>
            <td>${employeesList || '-'}</td>
        `;
        tbody.appendChild(row);
    });
    
    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="no-data">لا توجد بيانات متاحة</td></tr>';
    }
}

// إنشاء تقرير حسب القسم
function generateDepartmentReport() {
    const selectedDepartment = document.getElementById('departmentFilter').value;
    const tbody = document.getElementById('departmentReportBody');
    tbody.innerHTML = '';
    
    // الحصول على قائمة الأقسام
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))];
    const departmentsToProcess = selectedDepartment ? [selectedDepartment] : departments;
    
    // تجميع البيانات حسب القسم
    departmentsToProcess.forEach(department => {
        // الحصول على موظفي القسم
        const departmentEmployees = employees.filter(emp => emp.department === department);
        const employeeCodes = departmentEmployees.map(emp => emp.code);
        
        // الحصول على سجلات التسليم لموظفي القسم
        const departmentDeliveries = deliveryRecords.filter(record => 
            employeeCodes.includes(record.employee_code)
        );
        
        // تجميع البيانات حسب نوع العهدة
        const custodyTypeMap = {};
        
        departmentDeliveries.forEach(delivery => {
            const custodyItem = custodyItems.find(item => item.custody_code === delivery.custody_code);
            const custodyType = custodyItem ? custodyItem.type : (delivery.custody_type || 'غير محدد');
            
            if (!custodyTypeMap[custodyType]) {
                custodyTypeMap[custodyType] = {
                    delivered: 0,
                    returned: 0,
                    damaged: 0,
                    lost: 0,
                    employees: new Set()
                };
            }
            
            // إضافة الكمية المسلمة
            custodyTypeMap[custodyType].delivered += delivery.quantity;
            custodyTypeMap[custodyType].employees.add(delivery.employee_name);
            
            // بعد حذف قسم الاسترجاع، لا توجد كميات مسترجعة أو معطوبة أو مفقودة
        });
        
        // عرض البيانات لكل نوع عهدة في القسم
        Object.entries(custodyTypeMap).forEach(([type, data]) => {
            const remaining = data.delivered - data.returned;
            const employeesList = [...data.employees].join(', ');
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${department}</td>
                <td>${type}</td>
                <td>${data.delivered}</td>
                <td>${data.returned}</td>
                <td>${data.damaged}</td>
                <td>${data.lost}</td>
                <td>${data.employees.size}</td>
                <td>${employeesList}</td>
            `;
            tbody.appendChild(row);
        });
    });
    
    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="no-data">لا توجد بيانات متاحة</td></tr>';
    }
}

// تصدير إلى Excel
function exportToExcel(type) {
    let data = [];
    let headers = [];
    let fileName = '';
    let tableElement = null;
    
    // تحديد البيانات والعناوين حسب نوع التقرير
    switch (type) {
        case 'custody':
            headers = ['كود العهدة', 'اسم العهدة', 'نوع العهدة', 'الحالة', 'الكمية الكلية', 'الكمية المتاحة', 'تاريخ الإضافة', 'الإجراءات'];
            fileName = 'قائمة_العهد';
            
            // استخراج البيانات من جدول العهد
            tableElement = document.querySelector('.custody-table');
            if (tableElement) {
                const rows = tableElement.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    // تجاهل عمود الإجراءات
                    const cells = row.querySelectorAll('td:not(:last-child)');
                    if (cells.length > 0) {
                        data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                    }
                });
                // تحديث العناوين لتجاهل عمود الإجراءات
                headers.pop();
            }
            break;
            
        case 'undelivered':
            headers = ['كود العهدة', 'اسم العهدة', 'نوع العهدة', 'الكمية المتاحة', 'تاريخ الإضافة'];
            fileName = 'العهد_غير_المسلمة';
            
            // استخراج البيانات من جدول العهد غير المسلمة
            tableElement = document.querySelector('.undelivered-table');
            if (tableElement) {
                const rows = tableElement.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                    }
                });
            }
            break;
            
        case 'employee-report':
            headers = ['اسم الموظف', 'القسم', 'اسم العهدة', 'الكمية', 'تاريخ التسليم', 'الحالة'];
            fileName = 'تقرير_العهد_حسب_الموظف';
            
            // استخراج البيانات من جدول التقرير
            const employeeRows = document.querySelectorAll('#employeeReportBody tr');
            employeeRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                }
            });
            break;
            

            
        case 'pending-report':
            headers = ['اسم الموظف', 'القسم', 'اسم العهدة', 'الكمية الكلية', 'الكمية المسترجعة', 'الكمية المتبقية', 'تاريخ التسليم', 'المدة', 'الحالة'];
            fileName = 'تقرير_العهد_المعلقة';
            
            const pendingRows = document.querySelectorAll('#pendingReportBody tr');
            pendingRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                }
            });
            break;
            
        case 'type-report':
            headers = ['نوع العهدة', 'إجمالي الكمية', 'الكمية المتاحة', 'الكمية المسلمة', 'الكمية المسترجعة', 'الكمية المتبقية', 'المعطوب', 'المفقود', 'عدد الموظفين', 'قائمة الموظفين'];
            fileName = 'تقرير_العهد_حسب_النوع';
            
            const typeRows = document.querySelectorAll('#typeReportBody tr');
            typeRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                }
            });
            break;
            
        case 'department-report':
            headers = ['القسم', 'نوع العهدة', 'الكمية المسلمة', 'الكمية المسترجعة', 'الكمية المتبقية', 'المعطوب', 'المفقود', 'عدد الموظفين', 'قائمة الموظفين'];
            fileName = 'تقرير_العهد_حسب_القسم';
            
            const departmentRows = document.querySelectorAll('#departmentReportBody tr');
            departmentRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                }
            });
            break;
            
        case 'employee-custody':
            headers = ['كود العهدة', 'اسم العهدة', 'نوع العهدة', 'تاريخ الاستلام', 'الكمية المستلمة'];
            fileName = 'عهدة_موظف';
            
            // استخراج البيانات من جدول عهدة الموظف
            tableElement = document.querySelector('.report-table');
            if (tableElement) {
                const rows = tableElement.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                    }
                });
                
                // إضافة اسم الموظف للملف إذا كان متاحًا
                try {
                    const employeeNameElement = document.getElementById('reportEmployeeName');
                    if (employeeNameElement && employeeNameElement.textContent) {
                        fileName = 'عهدة_' + employeeNameElement.textContent.trim();
                    }
                } catch (error) {
                    console.error('خطأ في الحصول على اسم الموظف:', error);
                    // استخدام الاسم الافتراضي إذا حدث خطأ
                }
            }
            break;
            
        case 'delivery':
            headers = ['رقم العملية', 'كود العهدة', 'اسم العهدة', 'نوع العهدة', 'اسم الموظف', 'القسم', 'الكمية', 'تاريخ التسليم'];
            fileName = 'سجل_تسليم_العهد';
            
            // استخراج البيانات من جدول سجل التسليم
            tableElement = document.querySelector('.delivery-table');
            if (tableElement) {
                const rows = tableElement.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                    }
                });
            }
            break;

            
        default:
            showNotification('نوع التقرير غير معروف', 'error');
            return;
    }
    
    // التحقق من وجود بيانات
    if (data.length === 0) {
        showNotification('لا توجد بيانات للتصدير', 'warning');
        return;
    }
    
    try {
        // إنشاء ملف Excel مع دعم RTL
        const wb = XLSX.utils.book_new();
        
        // تحضير البيانات مع الرؤوس
        const wsData = [headers, ...data];
        
        // إنشاء worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData);
        
        // إعداد اتجاه النص من اليمين إلى الشمال (RTL)
        if (ws['!ref']) {
            const range = XLSX.utils.decode_range(ws['!ref']);
            
            // تطبيق تنسيق RTL على جميع الخلايا
            for (let R = range.s.r; R <= range.e.r; ++R) {
                for (let C = range.s.c; C <= range.e.c; ++C) {
                    const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
                    if (!ws[cellAddress]) continue;
                    
                    // إضافة تنسيق RTL للخلية
                    if (!ws[cellAddress].s) ws[cellAddress].s = {};
                    ws[cellAddress].s.alignment = {
                        horizontal: 'right',
                        readingOrder: 2, // RTL
                        textRotation: 0,
                        vertical: 'center',
                        wrapText: true
                    };
                    
                    // تنسيق خاص للرؤوس (الصف الأول)
                    if (R === 0) {
                        ws[cellAddress].s.font = { 
                            bold: true,
                            size: 12,
                            name: 'Arial'
                        };
                        ws[cellAddress].s.fill = { 
                            fgColor: { rgb: "9C27B0" } // بنفسجي للعهد
                        };
                    }
                    
                    // إضافة حدود للخلايا
                    ws[cellAddress].s.border = {
                        top: { style: 'thin', color: { rgb: 'CCCCCC' } },
                        bottom: { style: 'thin', color: { rgb: 'CCCCCC' } },
                        left: { style: 'thin', color: { rgb: 'CCCCCC' } },
                        right: { style: 'thin', color: { rgb: 'CCCCCC' } }
                    };
                }
            }
            
            // تحديد عرض الأعمدة
            const colWidths = headers.map(() => ({ wch: 18 }));
            ws['!cols'] = colWidths;
            
            // إضافة فلتر تلقائي
            ws['!autofilter'] = { ref: ws['!ref'] };
        }
        
        // إضافة worksheet إلى workbook
        const sheetName = fileName.replace(/_/g, ' ');
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
        
        // إعداد خصائص workbook للغة العربية
        wb.Props = {
            Title: fileName,
            Subject: 'تقرير العهد',
            Author: 'نظام إدارة الموظفين',
            CreatedDate: new Date(),
            Language: 'ar-SA'
        };
        
        // إعداد خصائص العرض مع دعم RTL
        wb.Workbook = {
            Views: [{
                RTL: true // تفعيل وضع RTL للـ workbook
            }]
        };
        
        // تصدير الملف
        XLSX.writeFile(wb, `${fileName}_${new Date().toISOString().split('T')[0]}.xlsx`);
        
        showNotification('تم تصدير التقرير إلى Excel بنجاح', 'success');
        
    } catch (error) {
        console.error('خطأ في تصدير Excel، التبديل إلى CSV:', error);
        
        // Fallback إلى CSV في حالة فشل Excel
        let csvContent = "\uFEFF";
        
        // إضافة العناوين
        csvContent += headers.map(header => `"${header}"`).join(';') + '\r\n';
        
        // إضافة البيانات
        data.forEach(row => {
            const processedRow = row.map(cell => {
                if (cell !== null && cell !== undefined) {
                    const cellStr = String(cell);
                    return '"' + cellStr.replace(/"/g, '""') + '"';
                }
                return '';
            });
            csvContent += processedRow.join(';') + '\r\n';
        });
        
        // تنزيل الملف
        const encodedUri = encodeURI("data:text/csv;charset=utf-8," + csvContent);
        const link = document.createElement('a');
        link.setAttribute('href', encodedUri);
        link.setAttribute('download', `${fileName}_${new Date().toISOString().split('T')[0]}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showNotification('تم تصدير التقرير إلى CSV بنجاح', 'success');
    }
}

// فحص الصلاحيات
function hasPermission(permission) {
  try {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
    const result = permissions[permission] === true;
    console.log(`[Custody] hasPermission(${permission}) = ${result}`, permissions);
    return result;
  } catch (error) {
    console.error('خطأ في قراءة الصلاحيات:', error);
    return false;
  }
}

// تعديل عهدة
function editCustody(id) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_custody')) {
        showNotification('ليس لديك صلاحية لتعديل العهد', 'error');
        return;
    }

    const custody = custodyItems.find(item => item.id === id);
    if (custody) {
        document.getElementById('editCustodyId').value = custody.id;

        // استخدام الأسماء الصحيحة للحقول
        const custodyName = custody.custody_name || custody.name || '';
        const custodyType = custody.custody_type || custody.type || '';
        const custodyStatus = custody.status || 'جديدة';
        const custodyQuantity = custody.quantity || 1;

        document.getElementById('editCustodyName').value = custodyName;
        document.getElementById('editCustodyStatus').value = custodyStatus;
        document.getElementById('editCustodyQuantity').value = custodyQuantity;

        // تحديث خيارات نوع العهدة ديناميكياً
        updateCustodyTypeOptions(custodyType);

        console.log('تم تحميل بيانات العهدة للتعديل:', {
            id: custody.id,
            name: custodyName,
            type: custodyType,
            status: custodyStatus,
            quantity: custodyQuantity
        });

        document.getElementById('editCustodyModal').style.display = 'block';
    }
}

// تحديث خيارات نوع العهدة ديناميكياً
function updateCustodyTypeOptions(currentType) {
    const typeSelect = document.getElementById('editCustodyType');
    if (!typeSelect) return;

    // الحصول على جميع أنواع العهدة الموجودة في البيانات
    const existingTypes = [...new Set(custodyItems.map(item => {
        return item.custody_type || item.type || '';
    }).filter(type => type.trim() !== ''))];

    // الأنواع الافتراضية
    const defaultTypes = ['لابتوب', 'موبايل', 'شاشة', 'طابعة', 'كيبورد', 'ماوس', 'الكترونيات', 'أثاث', 'أخرى'];

    // دمج الأنواع الموجودة مع الافتراضية
    const allTypes = [...new Set([...defaultTypes, ...existingTypes])];

    // مسح الخيارات الحالية
    typeSelect.innerHTML = '';

    // إضافة الخيارات
    allTypes.forEach(type => {
        const option = document.createElement('option');
        option.value = type;
        option.textContent = type;
        if (type === currentType) {
            option.selected = true;
        }
        typeSelect.appendChild(option);
    });

    // إذا لم يتم العثور على النوع الحالي، أضفه
    if (currentType && !allTypes.includes(currentType)) {
        const option = document.createElement('option');
        option.value = currentType;
        option.textContent = currentType;
        option.selected = true;
        typeSelect.appendChild(option);
    }

    console.log('تم تحديث خيارات نوع العهدة:', allTypes);
    console.log('النوع المحدد:', currentType);
}

// تأكيد حذف العهدة
function confirmDeleteCustody(id) {
    console.log('🔍 تم استدعاء confirmDeleteCustody للعهدة:', id);
    showConfirmationNotification(
        'هل أنت متأكد من حذف هذه العهدة؟',
        'لا يمكن التراجع عن هذا الإجراء.',
        () => deleteCustody(id)
    );
}

// حذف عهدة
async function deleteCustody(id) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_custody')) {
        showNotification('ليس لديك صلاحية لحذف العهد', 'error');
        return;
    }

    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            showNotification('تم حذف العهدة بنجاح', 'success');
            await loadCustodyItems();
            calculateAvailableQuantities();
            displayCustodyItemsWithDataTables();
            displayUndeliveredItems();
            loadAvailableCustodyForDelivery();
        } else {
            const error = await response.json();
            showNotification(error.error || 'خطأ في حذف العهدة', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف العهدة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// إعداد مستمعي أحداث النافذة المنبثقة
function setupModalEventListeners() {
    // نافذة تعديل العهدة
    const custodyModal = document.getElementById('editCustodyModal');
    const custodyCloseBtn = custodyModal.querySelector('.close');
    const custodyCancelBtn = custodyModal.querySelector('.cancel-btn');
    const custodySaveBtn = document.getElementById('saveEditBtn');

    custodyCloseBtn.addEventListener('click', () => {
        custodyModal.style.display = 'none';
    });

    custodyCancelBtn.addEventListener('click', () => {
        custodyModal.style.display = 'none';
    });

    custodySaveBtn.addEventListener('click', saveEditedCustody);

    // نافذة تعديل سجل التسليم
    const deliveryModal = document.getElementById('editDeliveryModal');
    const deliveryCloseBtn = deliveryModal.querySelector('.close');
    const deliveryCancelBtn = deliveryModal.querySelector('.cancel-btn');
    const deliverySaveBtn = document.getElementById('saveEditDeliveryBtn');

    deliveryCloseBtn.addEventListener('click', () => {
        deliveryModal.style.display = 'none';
    });

    deliveryCancelBtn.addEventListener('click', () => {
        deliveryModal.style.display = 'none';
    });

    if (deliverySaveBtn) {
        console.log('✅ تم العثور على زر حفظ التسليم وإضافة event listener');
        deliverySaveBtn.addEventListener('click', saveEditedDelivery);
    } else {
        console.error('❌ لم يتم العثور على زر حفظ التسليم');
    }



    // تم تعطيل مراجع الإرجاع مؤقتاً
    // returnCloseBtn.addEventListener('click', () => {
    //     returnModal.style.display = 'none';
    // });

    // returnCancelBtn.addEventListener('click', () => {
    //     returnModal.style.display = 'none';
    // });

    // returnSaveBtn.addEventListener('click', saveEditedReturn);

    // إغلاق النوافذ عند النقر خارجها
    window.addEventListener('click', (event) => {
        if (event.target === custodyModal) {
            custodyModal.style.display = 'none';
        }
        if (event.target === deliveryModal) {
            deliveryModal.style.display = 'none';
        }

    });
}

// حفظ تعديل العهدة
async function saveEditedCustody() {
    const id = document.getElementById('editCustodyId').value;
    const formData = {
        name: document.getElementById('editCustodyName').value,
        type: document.getElementById('editCustodyType').value,
        status: document.getElementById('editCustodyStatus').value,
        quantity: parseInt(document.getElementById('editCustodyQuantity').value)
    };
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(formData)
        });
        
        if (response.ok) {
            showNotification('تم تحديث العهدة بنجاح', 'success');
            document.getElementById('editCustodyModal').style.display = 'none';
            await loadCustodyItems();
            calculateAvailableQuantities();
            displayCustodyItemsWithDataTables();
            displayUndeliveredItems();
            loadAvailableCustodyForDelivery();
        } else {
            showNotification('خطأ في تحديث العهدة', 'error');
        }
    } catch (error) {
        console.error('خطأ في تحديث العهدة:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

// استخدام دالة showNotification من shared-utils.js

// دوال تعديل وحذف سجلات التسليم
function editDelivery(deliveryId) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_deliver_custody')) {
        showNotification('ليس لديك صلاحية لتعديل سجلات التسليم', 'error');
        return;
    }

    const delivery = deliveryRecords.find(record => record.id === deliveryId);
    if (!delivery) {
        showNotification('سجل التسليم غير موجود', 'error');
        return;
    }

    // ملء النافذة المنبثقة بالبيانات الحالية
    document.getElementById('editDeliveryId').value = delivery.id;
    document.getElementById('editDeliveryOperationNumber').value = delivery.operation_number;
    document.getElementById('editDeliveryEmployeeCode').value = delivery.employee_code;
    document.getElementById('editDeliveryEmployeeName').value = delivery.employee_name;
    document.getElementById('editDeliveryDepartment').value = delivery.department || '';
    document.getElementById('editDeliveryCustodyCode').value = delivery.custody_code;
    document.getElementById('editDeliveryCustodyName').value = delivery.custody_name || getCustodyName(delivery.custody_code);
    document.getElementById('editDeliveryCustodyType').value = delivery.custody_type;
    document.getElementById('editDeliveryQuantity').value = delivery.quantity;

    // تنسيق التاريخ للإدخال
    const deliveryDate = delivery.delivery_date;
    if (deliveryDate) {
        if (typeof DateUtils !== 'undefined') {
            document.getElementById('editDeliveryDate').value = DateUtils.formatDateForInput(deliveryDate);
        } else {
            // Fallback: تحويل التاريخ إلى صيغة YYYY-MM-DD
            const date = new Date(deliveryDate);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            document.getElementById('editDeliveryDate').value = `${year}-${month}-${day}`;
        }
    }

    document.getElementById('editDeliveryStatus').value = delivery.status;

    // فتح النافذة المنبثقة
    document.getElementById('editDeliveryModal').style.display = 'block';

    console.log('تم تحميل بيانات التسليم للتعديل');
}

// تأكيد حذف سجل التسليم
function confirmDeleteDelivery(deliveryId) {
    console.log('🔍 تم استدعاء confirmDeleteDelivery لسجل التسليم:', deliveryId);
    showConfirmationNotification(
        'هل أنت متأكد من حذف سجل التسليم؟',
        'لا يمكن التراجع عن هذا الإجراء.',
        () => deleteDelivery(deliveryId)
    );
}

function deleteDelivery(deliveryId) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_deliver_custody')) {
        showNotification('ليس لديك صلاحية لحذف سجلات التسليم', 'error');
        return;
    }

    const token = localStorage.getItem('token');
    fetch(`${API_URL}/api/custody/delivery/${deliveryId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (response.ok) {
            showNotification('تم حذف سجل التسليم بنجاح', 'success');
            loadDeliveryRecords();
            loadCustodyItems().then(() => {
                calculateAvailableQuantities();
                loadAvailableCustodyForDelivery();
            });
            displayDeliveryRecordsWithDataTables();
        } else {
            throw new Error('فشل في حذف سجل التسليم');
        }
    })
    .catch(error => {
        console.error('خطأ في حذف سجل التسليم:', error);
        showNotification('خطأ في حذف سجل التسليم', 'error');
    });
}



// حفظ تعديلات سجل التسليم
async function saveEditedDelivery() {
    console.log('🔧 بدء حفظ تعديلات سجل التسليم');
    const deliveryId = document.getElementById('editDeliveryId').value;
    console.log('معرف التسليم:', deliveryId);
    const formData = {
        employee_code: document.getElementById('editDeliveryEmployeeCode').value,
        employee_name: document.getElementById('editDeliveryEmployeeName').value,
        department: document.getElementById('editDeliveryDepartment').value,
        custody_code: document.getElementById('editDeliveryCustodyCode').value,
        custody_name: document.getElementById('editDeliveryCustodyName').value,
        custody_type: document.getElementById('editDeliveryCustodyType').value,
        quantity: parseInt(document.getElementById('editDeliveryQuantity').value),
        delivery_date: document.getElementById('editDeliveryDate').value,
        status: document.getElementById('editDeliveryStatus').value
    };

    // التحقق من صحة البيانات
    if (!formData.employee_code || !formData.employee_name || !formData.custody_code ||
        !formData.delivery_date || !formData.quantity) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/api/custody/delivery/${deliveryId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(formData)
        });

        if (response.ok) {
            showNotification('تم تحديث سجل التسليم بنجاح', 'success');
            document.getElementById('editDeliveryModal').style.display = 'none';
            await loadDeliveryRecords();
            displayDeliveryRecordsWithDataTables();
        } else {
            const error = await response.json();
            showNotification(error.error || 'خطأ في تحديث سجل التسليم', 'error');
        }
    } catch (error) {
        console.error('خطأ في تحديث سجل التسليم:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}





// تعيين التاريخ الحالي
function setCurrentDate() {
    const today = new Date().toISOString().split('T')[0];
    const deliveryDateInput = document.getElementById('deliveryDate');

    if (deliveryDateInput) {
        deliveryDateInput.value = today;
    }
}



// طباعة تفاصيل العهدة
function printCustodyDetails(custody, deliveries) {
    const printWindow = window.open('', '', 'width=800,height=600');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تفاصيل مستلمي العهدة - ${custody.name}</title>
            <style>
                @page { size: A4; margin: 1cm; }
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                h1 { text-align: center; color: #333; margin-bottom: 20px; }
                h3 { margin-top: 20px; color: #555; border-bottom: 2px solid #2196F3; padding-bottom: 5px; }
                .info-container { display: flex; justify-content: space-between; margin-bottom: 20px; background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
                .info-item { padding: 10px; }
                .info-label { font-weight: bold; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                th, td { border: 1px solid #ddd; padding: 10px; text-align: right; }
                th { background-color: #2196F3; color: white; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .summary { margin-top: 20px; display: flex; justify-content: space-around; }
                .summary-item { text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); background-color: #f2f2f2; width: 30%; }
                .summary-label { font-weight: bold; color: #333; }
                .summary-value { font-size: 1.2em; margin-top: 5px; color: #333; font-weight: bold; }
                .btn { display: inline-block; padding: 8px 20px; border-radius: 4px; cursor: pointer; font-weight: bold; text-align: center; margin: 0 10px; min-width: 100px; border: none; }
                .print-btn { background-color: #2196F3; color: white; }
                .close-btn { background-color: #f44336; color: white; }
                @media print { 
                    .no-print { display: none; } 
                    body { margin: 0; padding: 10px; }
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; page-break-after: auto; }
                    h1 { margin-top: 0; }
                }
            </style>
        </head>
        <body>
            <h1>تفاصيل مستلمي العهدة</h1>
            
            <div class="info-container">
                <div class="info-item"><span class="info-label">كود العهدة:</span> ${custody.custody_code}</div>
                <div class="info-item"><span class="info-label">اسم العهدة:</span> ${custody.name}</div>
                <div class="info-item"><span class="info-label">نوع العهدة:</span> ${custody.type}</div>
            </div>
            
            <div class="summary">
                <div class="summary-item">
                    <div class="summary-label">إجمالي العدد</div>
                    <div class="summary-value">${custody.quantity}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">المسلم</div>
                    <div class="summary-value">${deliveries.reduce((sum, delivery) => sum + parseInt(delivery.quantity), 0)}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">المتاح</div>
                    <div class="summary-value">${custody.available_quantity}</div>
                </div>
            </div>
            
            <h3>قائمة المستلمين</h3>
            <table>
                <thead>
                    <tr>
                        <th>كود الموظف</th>
                        <th>اسم الموظف</th>
                        <th>القسم</th>
                        <th>الكمية</th>
                        <th>تاريخ التسليم</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${deliveries.map(delivery => {
                        const employee = employees.find(emp => emp.code === delivery.employee_code);
                        const departmentName = employee ? employee.department : 'غير معروف';
                        return `
                            <tr>
                                <td>${delivery.employee_code}</td>
                                <td>${delivery.employee_name}</td>
                                <td>${departmentName}</td>
                                <td>${delivery.quantity}</td>
                                <td>${new Date(delivery.delivery_date).toLocaleDateString('ar-EG')}</td>
                                <td>${delivery.status}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
            
            <div class="no-print" style="margin-top: 30px; text-align: center;">
                <button onclick="window.print()" class="btn print-btn">طباعة</button>
                <button onclick="window.close()" class="btn close-btn">إغلاق</button>
            </div>
            
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                    }, 500);
                }
            </script>
        </body>
        </html>
    `);
    printWindow.document.close();
}

// تصدير تفاصيل العهدة إلى ملف Excel بنفس تنسيق الإجازات
function exportCustodyDetails(custody, deliveries) {
    try {
        // منع التنفيذ المتعدد
        if (window.isExportingCustodyDetails) {
            return;
        }

        window.isExportingCustodyDetails = true;

        // تعطيل الزر مؤقتاً
        const exportBtn = document.querySelector('.export-btn') || document.getElementById('exportCustodyDetailsBtn');
        if (exportBtn) {
            exportBtn.disabled = true;
            exportBtn.style.opacity = '0.6';
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
        }

        // إنشاء مصفوفة البيانات بنفس طريقة الإجازات
        const data = [];

        // إضافة معلومات العهدة في البداية
        data.push(['تقرير تفاصيل مستلمي العهدة']);
        data.push(['']);
        data.push(['كود العهدة', custody.custody_code || 'غير محدد']);
        data.push(['اسم العهدة', custody.name || 'غير محدد']);
        data.push(['نوع العهدة', custody.type || 'غير محدد']);
        data.push(['']);

        // إضافة الإحصائيات
        const totalDelivered = deliveries.reduce((sum, delivery) => sum + parseInt(delivery.quantity || 0), 0);
        data.push(['إجمالي الكمية', custody.quantity || 0]);
        data.push(['الكمية المسلمة', totalDelivered]);
        data.push(['الكمية المتاحة', custody.available_quantity || 0]);
        data.push(['عدد المستلمين', deliveries.length]);
        data.push(['']);

        // إضافة رأس جدول المستلمين
        data.push(['قائمة المستلمين']);
        data.push(['']);
        data.push([
            'رقم العملية',
            'كود الموظف',
            'اسم الموظف',
            'الإدارة',
            'الكمية المستلمة',
            'تاريخ التسليم',
            'الحالة'
        ]);

        // إضافة بيانات المستلمين
        if (deliveries.length === 0) {
            data.push(['لا يوجد مستلمين لهذه العهدة']);
        } else {
            deliveries.forEach((delivery, index) => {
                const employee = employees.find(emp => emp.code === delivery.employee_code);
                const deliveryDate = new Date(delivery.delivery_date);

                data.push([
                    delivery.operation_number || (index + 1),
                    delivery.employee_code || 'غير محدد',
                    delivery.employee_name || 'غير محدد',
                    delivery.department || employee?.department || 'غير محدد',
                    delivery.quantity || 0,
                    deliveryDate.toLocaleDateString('ar-EG'),
                    delivery.status || 'مسلمة'
                ]);
            });
        }

        // تحويل البيانات إلى CSV بنفس طريقة الإجازات
        let csvContent = "\uFEFF"; // إضافة BOM للدعم الصحيح للغة العربية
        data.forEach(rowArray => {
            const row = rowArray.join(";\t"); // استخدام الفاصلة المنقوطة وعلامة التبويب لتجنب مشاكل الفواصل
            csvContent += row + "\r\n";
        });

        // إنشاء اسم الملف
        const fileName = `تفاصيل_مستلمي_العهدة_${custody.custody_code}.csv`;

        // إنشاء رابط التنزيل
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.setAttribute("href", url);
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // إظهار رسالة نجاح
        if (typeof showNotification === 'function') {
            showNotification('تم تصدير تفاصيل العهدة بنجاح', 'success');
        } else {
            alert('تم تصدير تفاصيل العهدة بنجاح');
        }

    } catch (error) {
        console.error('خطأ في تصدير تفاصيل العهدة:', error);
        if (typeof showNotification === 'function') {
            showNotification('حدث خطأ أثناء تصدير التفاصيل', 'error');
        } else {
            alert('حدث خطأ أثناء تصدير التفاصيل');
        }
    } finally {
        // إعادة تفعيل الزر
        setTimeout(() => {
            const exportBtn = document.querySelector('.export-btn') || document.getElementById('exportCustodyDetailsBtn');
            if (exportBtn) {
                exportBtn.disabled = false;
                exportBtn.style.opacity = '1';
                exportBtn.innerHTML = '<i class="fas fa-file-excel"></i> تصدير إلى Excel';
            }
            window.isExportingCustodyDetails = false;
        }, 1500);
    }
}

// التحقق من المحتوى المحدد من URL
function checkSelectedContent() {
  // قراءة التبويب من URL
  const urlParams = new URLSearchParams(window.location.search);
  const tabFromUrl = urlParams.get('tab');

  if (tabFromUrl) {
    // تحويل اسم التبويب من URL إلى اسم المحتوى
    let contentType = '';
    switch(tabFromUrl) {
      case 'add-custody':
        contentType = 'add-custody';
        break;
      case 'custody-delivery':
        contentType = 'deliver-custody';
        break;
      case 'custody-list':
        contentType = 'undelivered-custody';
        break;
      case 'custody-reports':
        contentType = 'employee-custody-report';
        break;
      default:
        contentType = 'add-custody';
    }
    showContent(contentType);
  } else {
    // عرض المحتوى الافتراضي (إضافة عهدة)
    showContent('add-custody');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.classList.remove('active');
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.classList.add('active');
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-custody') {
        pageTitle.textContent = 'إضافة عهدة';
      } else if (contentType === 'deliver-custody') {
        pageTitle.textContent = 'تسليم العهد';

      } else if (contentType === 'undelivered-custody') {
        pageTitle.textContent = 'العهد غير المسلمة';
      } else if (contentType === 'employee-custody-report') {
        pageTitle.textContent = 'عهدة الموظف';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'undelivered-custody') {
      loadUndeliveredCustody();
    } else if (contentType === 'employee-custody-report') {
      // يمكن إضافة تحميل بيانات تقرير الموظف هنا إذا لزم الأمر
    }
  } else {
    console.error('لم يتم العثور على المحتوى:', contentType);
  }
}

// ===== وظائف DataTables للعهد =====

// دالة للتحقق من تحميل جميع المكتبات المطلوبة
function checkLibrariesLoaded() {
  return typeof $ !== 'undefined' &&
         typeof $.fn.DataTable !== 'undefined' &&
         document.getElementById('custodyTable') !== null;
}

// تهيئة DataTables للعهد
function initializeCustodyDataTables() {
  // التحقق من وجود التوكن
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('لا يوجد توكن مصادقة - لا يمكن تهيئة DataTables');
    showNotification('يرجى تسجيل الدخول أولاً', 'error');
    setTimeout(() => {
      window.location.href = 'login.html';
    }, 2000);
    return;
  }

  // التحقق من تحميل جميع المكتبات المطلوبة
  if (!checkLibrariesLoaded()) {
    console.log('انتظار تحميل المكتبات المطلوبة...');
    setTimeout(initializeCustodyDataTables, 1000); // إعادة المحاولة بعد ثانية
    return;
  }

  try {
    console.log('🚀 بدء تهيئة DataTables للعهد...');
    console.log('📡 API URL:', `${API_URL}/api/custody/datatables`);

    // تهيئة DataTables
    const table = $('#custodyTable').DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: `${API_URL}/api/custody/datatables`,
        type: 'GET',
        beforeSend: function(xhr) {
          const token = localStorage.getItem('token');
          if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
          }
        },
        error: function(xhr, error, code) {
          console.error('❌ خطأ في تحميل بيانات العهد:', error, xhr);
          console.error('📊 تفاصيل الخطأ:', {
            status: xhr.status,
            statusText: xhr.statusText,
            responseText: xhr.responseText
          });

          if (xhr.status === 401 || xhr.status === 403) {
            if (typeof handleTokenExpired === 'function') {
              handleTokenExpired();
            } else {
              showNotification('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'error');
              setTimeout(() => {
                window.location.href = 'login.html';
              }, 2000);
            }
          } else if (xhr.status === 404) {
            console.error('❌ API endpoint غير موجود: /api/custody/datatables');
            showNotification('خطأ: API endpoint للعهد غير موجود. يرجى التحقق من إعدادات الخادم.', 'error');
          } else {
            showNotification('حدث خطأ في تحميل بيانات العهد. يرجى المحاولة مرة أخرى.', 'error');
          }
        }
      },
      columns: [
        { data: 0, title: 'كود العهد', width: '10%', className: 'text-center' },
        { data: 1, title: 'اسم العهدة', width: '20%' },
        { data: 2, title: 'النوع', width: '12%' },
        { data: 3, title: 'الحالة', width: '10%', className: 'text-center' },
        { data: 4, title: 'العدد', width: '8%', className: 'text-center' },
        { data: 5, title: 'الكمية المصروفة', width: '10%', className: 'text-center' },
        { data: 6, title: 'الكمية المتبقية', width: '10%', className: 'text-center' },
        { data: 7, title: 'تاريخ الإضافة', width: '10%', className: 'text-center' },
        { data: 8, title: 'الإجراءات', orderable: false, searchable: false, width: '24%', className: 'text-center' }
      ],
      order: [[0, 'desc']], // ترتيب حسب كود العهد (الأحدث أولاً)
      pageLength: 100, // العدد الافتراضي للخانات
      language: {
        search: 'البحث:',
        lengthMenu: 'عرض _MENU_ سجل',
        info: 'عرض _START_ إلى _END_ من _TOTAL_ سجل',
        infoEmpty: 'لا توجد عهد مضافة',
        infoFiltered: '(مفلتر من _MAX_ سجل إجمالي)',
        paginate: {
          first: 'الأول',
          last: 'الأخير',
          next: 'التالي',
          previous: 'السابق'
        },
        processing: 'جاري تحميل العهد...',
        emptyTable: 'لم يتم إضافة أي عهد بعد',
        zeroRecords: 'لم يتم العثور على عهد مطابقة لبحثك',
        loadingRecords: 'جاري تحميل العهد...',
        infoThousands: ',',
        searchPlaceholder: 'ابحث في العهد...'
      },
      responsive: true,
      dom: 'Brtip', // إزالة f (البحث) لأننا نستخدم البحث المتقدم
      deferRender: true, // تحسين الأداء للجداول الكبيرة
      stateSave: true, // حفظ حالة الجدول (الصفحة، الترتيب، إلخ)
      buttons: [
        {
          extend: 'excel',
          text: 'تصدير إلى Excel',
          className: 'btn btn-success',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7] // استبعاد عمود الإجراءات
          }
        }
      ],
      drawCallback: function(settings) {
        // إعادة ربط أحداث الأزرار بعد كل رسم للجدول
        bindCustodyActionButtons();

        // تطبيق الصلاحيات على الأزرار
        applyCustodyPermissionsToButtons();
      },
      initComplete: function() {
        console.log('✅ تم تهيئة DataTables للعهد بنجاح');
        console.log('📊 الجدول جاهز لعرض البيانات');
      }
    });

    // حفظ مرجع الجدول للاستخدام في أماكن أخرى
    window.custodyDataTable = table;

    // إضافة وظائف التحكم في جدول العهد
    setTimeout(function() {
      try {
        setupCustodyTableControls(table);
      } catch (error) {
        console.error('خطأ في إعداد التحكم في جدول العهد:', error);
      }
    }, 500);

  } catch (error) {
    console.error('خطأ في تهيئة DataTables للعهد:', error);
    showNotification('حدث خطأ في تهيئة الجدول. يرجى إعادة تحميل الصفحة.', 'error');
  }
}

// ربط أحداث أزرار الإجراءات في DataTables
function bindCustodyActionButtons() {
  // ربط أزرار التعديل
  $('.edit-custody-btn').off('click').on('click', function() {
    const custodyId = $(this).data('custody-id');

    // التحقق من الصلاحيات
    const permissionFunc = window.hasPermission || hasPermission;
    if (typeof permissionFunc === 'function' && !permissionFunc('edit_custody')) {
      showNotification('ليس لديك صلاحية لتعديل العهد', 'error');
      return;
    }

    // استدعاء دالة التعديل الموجودة
    editCustody(custodyId);
  });

  // ربط أزرار الحذف
  $('.delete-custody-btn').off('click').on('click', function() {
    const custodyId = $(this).data('custody-id');

    // التحقق من الصلاحيات
    const permissionFunc = window.hasPermission || hasPermission;
    if (typeof permissionFunc === 'function' && !permissionFunc('delete_custody')) {
      showNotification('ليس لديك صلاحية لحذف العهد', 'error');
      return;
    }

    // استدعاء دالة التأكيد المحسنة
    confirmDeleteCustody(custodyId);
  });

  // ربط أزرار تفاصيل المستلم
  $('.details-custody-btn').off('click').on('click', function() {
    const custodyCode = $(this).data('custody-code');
    const custodyName = $(this).data('custody-name');

    // استدعاء دالة عرض تفاصيل المستلم
    showCustodyRecipientDetails(custodyCode, custodyName);
  });
}

// تطبيق الصلاحيات على الأزرار
function applyCustodyPermissionsToButtons() {
  // التحقق من وجود دالة hasPermission
  const permissionFunc = window.hasPermission || hasPermission;
  if (typeof permissionFunc !== 'function') {
    console.warn('دالة hasPermission غير متاحة');
    return;
  }

  // إخفاء أزرار التعديل إذا لم تكن هناك صلاحية
  if (!permissionFunc('edit_custody')) {
    $('.edit-custody-btn').hide();
  }

  // إخفاء أزرار الحذف إذا لم تكن هناك صلاحية
  if (!permissionFunc('delete_custody')) {
    $('.delete-custody-btn').hide();
  }

  // زر تفاصيل المستلم متاح للجميع (لا يحتاج صلاحيات خاصة)
  // يمكن إضافة صلاحية خاصة هنا إذا لزم الأمر
}

// دالة عرض تفاصيل المستلم
async function showCustodyRecipientDetails(custodyCode, custodyName) {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/api/custody/delivery?custody_code=${custodyCode}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error('فشل في جلب تفاصيل المستلمين');
    }

    const deliveries = await response.json();

    // فلترة التسليمات الخاصة بهذه العهدة فقط
    const custodyDeliveries = deliveries.filter(delivery =>
      delivery.custody_code == custodyCode && delivery.status === 'مسلم'
    );

    // إنشاء محتوى النافذة المنبثقة
    let modalContent = `
      <div class="modal-overlay" id="custodyDetailsModal">
        <div class="modal-content custody-details-modal">
          <div class="modal-header blue-header">
            <h3>تفاصيل مستلمي العهدة</h3>
            <button class="close-modal-btn" onclick="closeCustodyDetailsModal()">&times;</button>
          </div>
          <div class="modal-body">
            <div class="custody-info">
              <h4>معلومات العهدة</h4>
              <p><strong>كود العهدة:</strong> ${custodyCode}</p>
              <p><strong>اسم العهدة:</strong> ${custodyName}</p>
            </div>
            <div class="recipients-info">
              <h4>قائمة المستلمين</h4>
    `;

    if (custodyDeliveries.length === 0) {
      modalContent += '<p class="no-recipients">لا يوجد مستلمين لهذه العهدة</p>';
    } else {
      modalContent += `
        <table class="recipients-table">
          <thead>
            <tr>
              <th>كود الموظف</th>
              <th>اسم الموظف</th>
              <th>الإدارة</th>
              <th>الكمية</th>
              <th>تاريخ التسليم</th>
              <th>رقم العملية</th>
            </tr>
          </thead>
          <tbody>
      `;

      custodyDeliveries.forEach(delivery => {
        modalContent += `
          <tr>
            <td>${delivery.employee_code}</td>
            <td>${delivery.employee_name}</td>
            <td>${delivery.department || 'غير محدد'}</td>
            <td><strong>${delivery.quantity}</strong></td>
            <td>${new Date(delivery.delivery_date).toLocaleDateString('ar-EG')}</td>
            <td>${delivery.operation_number || 'غير محدد'}</td>
          </tr>
        `;
      });

      modalContent += `
          </tbody>
        </table>
        <div class="summary-info">
          <p><strong>إجمالي المستلمين:</strong> ${custodyDeliveries.length}</p>
          <p><strong>إجمالي الكمية المسلمة:</strong> ${custodyDeliveries.reduce((sum, d) => sum + parseInt(d.quantity), 0)}</p>
        </div>
      `;
    }

    modalContent += `
            </div>
          </div>
          <div class="modal-footer">
            <button class="print-btn" onclick="printCustodyDetails()">
              <i class="fas fa-print"></i> طباعة
            </button>
            <button class="export-btn" onclick="exportCustodyDetailsFromModal('${custodyCode}', '${custodyName}')">
              <i class="fas fa-file-excel"></i> تصدير إلى Excel
            </button>
            <button class="close-btn" onclick="closeCustodyDetailsModal()">
              <i class="fas fa-times"></i> إغلاق
            </button>
          </div>
        </div>
      </div>
    `;

    // إضافة النافذة المنبثقة إلى الصفحة
    document.body.insertAdjacentHTML('beforeend', modalContent);

  } catch (error) {
    console.error('خطأ في جلب تفاصيل المستلمين:', error);
    showNotification('حدث خطأ في جلب تفاصيل المستلمين', 'error');
  }
}

// دالة إغلاق النافذة المنبثقة
function closeCustodyDetailsModal() {
  const modal = document.getElementById('custodyDetailsModal');
  if (modal) {
    modal.remove();
  }
}

// دالة تصدير تفاصيل العهدة من النافذة الديناميكية
async function exportCustodyDetailsFromModal(custodyCode, custodyName) {
  try {
    // منع التنفيذ المتعدد
    if (window.isExportingCustodyDetailsModal) {
      return;
    }

    window.isExportingCustodyDetailsModal = true;

    // تعطيل الزر مؤقتاً
    const exportBtn = document.querySelector('.export-btn');
    if (exportBtn) {
      exportBtn.disabled = true;
      exportBtn.style.opacity = '0.6';
      exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
    }

    // جلب بيانات التسليم من الخادم
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/api/custody/delivery?custody_code=${custodyCode}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error('فشل في جلب تفاصيل المستلمين');
    }

    const deliveries = await response.json();

    // فلترة التسليمات الخاصة بهذه العهدة فقط
    const custodyDeliveries = deliveries.filter(delivery =>
      delivery.custody_code == custodyCode && delivery.status === 'مسلم'
    );

    // إعداد بيانات العهدة - استخدم بيانات العهدة الأصلية لضمان تطابق الإجماليات مع قائمة العهد
    const matchedItem = Array.isArray(custodyItems) ? custodyItems.find(item => item.custody_code == custodyCode) : null;
    const deliveredSum = custodyDeliveries.reduce((sum, delivery) => sum + (parseInt(delivery.quantity || 0) || 0), 0);
    const custody = {
      custody_code: custodyCode,
      name: matchedItem?.name || custodyName,
      type: matchedItem?.type || 'غير محدد',
      status: matchedItem?.status || 'نشطة',
      quantity: matchedItem?.quantity ?? deliveredSum, // استخدم الكمية الأصلية إن وُجدت، وإلا مجموع المسلم
      available_quantity: matchedItem?.available_quantity ?? Math.max((matchedItem?.quantity ?? deliveredSum) - deliveredSum, 0),
      created_at: matchedItem?.created_at || new Date().toISOString()
    };

    // استدعاء دالة التصدير المحسنة
    exportCustodyDetails(custody, custodyDeliveries);

  } catch (error) {
    console.error('خطأ في تصدير تفاصيل العهدة:', error);
    if (typeof showNotification === 'function') {
      showNotification('حدث خطأ أثناء تصدير التفاصيل', 'error');
    } else {
      alert('حدث خطأ أثناء تصدير التفاصيل');
    }
  } finally {
    // إعادة تفعيل الزر
    setTimeout(() => {
      const exportBtn = document.querySelector('.export-btn');
      if (exportBtn) {
        exportBtn.disabled = false;
        exportBtn.style.opacity = '1';
        exportBtn.innerHTML = '<i class="fas fa-file-excel"></i> تصدير إلى Excel';
      }
      window.isExportingCustodyDetailsModal = false;
    }, 1500);
  }
}

// دالة طباعة تفاصيل العهدة
function printCustodyDetails() {
  const modalContent = document.querySelector('.custody-details-modal .modal-body').innerHTML;
  const printWindow = window.open('', '_blank');
  printWindow.document.write(`
    <html>
      <head>
        <title>تفاصيل مستلمي العهدة</title>
        <style>
          body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
          th { background-color: #f2f2f2; font-weight: bold; }
          .custody-info, .recipients-info { margin: 20px 0; }
          .summary-info { margin-top: 20px; font-weight: bold; }
          .no-recipients { text-align: center; color: #666; }
          @media print { .no-print { display: none; } }
        </style>
      </head>
      <body>
        ${modalContent}
        <div class="no-print" style="margin-top: 30px; text-align: center;">
          <button onclick="window.print()" class="btn">طباعة</button>
          <button onclick="window.close()" class="btn">إغلاق</button>
        </div>
      </body>
    </html>
  `);
  printWindow.document.close();
}

// ===== وظائف DataTables لتسليم العهد =====

// تهيئة DataTables لتسليم العهد
function initializeDeliveryDataTables() {
  // التحقق من وجود التوكن
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('لا يوجد توكن مصادقة - لا يمكن تهيئة DataTables');
    showNotification('يرجى تسجيل الدخول أولاً', 'error');
    setTimeout(() => {
      window.location.href = 'login.html';
    }, 2000);
    return;
  }

  // التحقق من تحميل جميع المكتبات المطلوبة
  if (!checkLibrariesLoaded() || !document.getElementById('deliveryTable')) {
    console.log('انتظار تحميل المكتبات المطلوبة لتسليم العهد...');
    setTimeout(initializeDeliveryDataTables, 1000);
    return;
  }

  try {
    console.log('🚀 بدء تهيئة DataTables لتسليم العهد...');
    console.log('📡 API URL:', `${API_URL}/api/custody/delivery/datatables`);

    // تهيئة DataTables
    const table = $('#deliveryTable').DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: `${API_URL}/api/custody/delivery/datatables`,
        type: 'GET',
        beforeSend: function(xhr) {
          const token = localStorage.getItem('token');
          if (token) {
            xhr.setRequestHeader('Authorization', `Bearer ${token}`);
          }
        },
        error: function(xhr, error, code) {
          console.error('❌ خطأ في تحميل بيانات تسليم العهد:', error, xhr);
          console.error('📊 تفاصيل الخطأ:', {
            status: xhr.status,
            statusText: xhr.statusText,
            responseText: xhr.responseText
          });

          if (xhr.status === 401 || xhr.status === 403) {
            if (typeof handleTokenExpired === 'function') {
              handleTokenExpired();
            } else {
              showNotification('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'error');
              setTimeout(() => {
                window.location.href = 'login.html';
              }, 2000);
            }
          } else if (xhr.status === 404) {
            console.error('❌ API endpoint غير موجود: /api/custody/delivery/datatables');
            showNotification('خطأ: API endpoint لتسليم العهد غير موجود. يرجى التحقق من إعدادات الخادم.', 'error');
          } else {
            showNotification('حدث خطأ في تحميل بيانات تسليم العهد. يرجى المحاولة مرة أخرى.', 'error');
          }
        }
      },
      columns: [
        { data: 0, title: 'رقم العملية', width: '8%', className: 'text-center' },
        { data: 1, title: 'كود الموظف', width: '8%', className: 'text-center' },
        { data: 2, title: 'اسم الموظف', width: '15%' },
        { data: 3, title: 'الإدارة', width: '10%' },
        { data: 4, title: 'كود العهد', width: '8%', className: 'text-center' },
        { data: 5, title: 'اسم العهدة', width: '15%' },
        { data: 6, title: 'نوع العهدة', width: '10%' },
        { data: 7, title: 'العدد', width: '6%', className: 'text-center' },
        { data: 8, title: 'التاريخ', width: '8%', className: 'text-center' },
        { data: 9, title: 'الحالة', width: '8%', className: 'text-center' },
        { data: 10, visible: false, searchable: false }, // عمود مخفي: رقم السجل (ID) للترتيب
        { data: 11, title: 'الإجراءات', orderable: false, searchable: false, width: '12%', className: 'text-center' }
      ],
      order: [[10, 'desc']], // ترتيب حسب أحدث إدخال (الأحدث أولاً) بغض النظر عن التاريخ
      pageLength: 100, // العدد الافتراضي للخانات
      language: {
        search: 'البحث:',
        lengthMenu: 'عرض _MENU_ سجل',
        info: 'عرض _START_ إلى _END_ من _TOTAL_ سجل',
        infoEmpty: 'لا توجد سجلات تسليم',
        infoFiltered: '(مفلتر من _MAX_ سجل إجمالي)',
        paginate: {
          first: 'الأول',
          last: 'الأخير',
          next: 'التالي',
          previous: 'السابق'
        },
        processing: 'جاري تحميل سجلات التسليم...',
        emptyTable: 'لم يتم تسليم أي عهد بعد',
        zeroRecords: 'لم يتم العثور على سجلات مطابقة لبحثك',
        loadingRecords: 'جاري تحميل سجلات التسليم...',
        infoThousands: ',',
        searchPlaceholder: 'ابحث في سجلات التسليم...'
      },
      responsive: true,
      dom: 'Brtip',
      deferRender: true,
      stateSave: true,
      buttons: [
        {
          extend: 'excel',
          text: 'تصدير إلى Excel',
          className: 'btn btn-success',
          exportOptions: {
            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9] // استبعاد عمود الإجراءات
          }
        }
      ],
      drawCallback: function(settings) {
        // إعادة ربط أحداث الأزرار بعد كل رسم للجدول
        bindDeliveryActionButtons();

        // تطبيق الصلاحيات على الأزرار
        applyDeliveryPermissionsToButtons();
      },
      initComplete: function() {
        console.log('✅ تم تهيئة DataTables لتسليم العهد بنجاح');
        console.log('📊 الجدول جاهز لعرض البيانات');
      }
    });

    // حفظ مرجع الجدول للاستخدام في أماكن أخرى
    window.deliveryDataTable = table;

    // إضافة وظائف التحكم في جدول التسليم
    setTimeout(function() {
      try {
        setupDeliveryTableControls(table);
      } catch (error) {
        console.error('خطأ في إعداد التحكم في جدول التسليم:', error);
      }
    }, 500);

  } catch (error) {
    console.error('خطأ في تهيئة DataTables لتسليم العهد:', error);
    showNotification('حدث خطأ في تهيئة الجدول. يرجى إعادة تحميل الصفحة.', 'error');
  }
}

// ربط أحداث أزرار الإجراءات في DataTables تسليم العهد
function bindDeliveryActionButtons() {
  // ربط أزرار التعديل
  $('.edit-delivery-btn').off('click').on('click', function() {
    const deliveryId = $(this).data('delivery-id');

    // التحقق من الصلاحيات
    const permissionFunc = window.hasPermission || hasPermission;
    if (typeof permissionFunc === 'function' && !permissionFunc('edit_deliver_custody')) {
      showNotification('ليس لديك صلاحية لتعديل سجلات التسليم', 'error');
      return;
    }

    // استدعاء دالة التعديل الموجودة
    editDelivery(deliveryId);
  });

  // ربط أزرار الحذف
  $('.delete-delivery-btn').off('click').on('click', function() {
    const deliveryId = $(this).data('delivery-id');

    // التحقق من الصلاحيات
    const permissionFunc = window.hasPermission || hasPermission;
    if (typeof permissionFunc === 'function' && !permissionFunc('delete_deliver_custody')) {
      showNotification('ليس لديك صلاحية لحذف سجلات التسليم', 'error');
      return;
    }

    // استدعاء دالة التأكيد المحسنة
    confirmDeleteDelivery(deliveryId);
  });

  // تم حذف زر الطباعة
}

// تطبيق الصلاحيات على أزرار تسليم العهد
function applyDeliveryPermissionsToButtons() {
  // التحقق من وجود دالة hasPermission
  const permissionFunc = window.hasPermission || hasPermission;
  if (typeof permissionFunc !== 'function') {
    console.warn('دالة hasPermission غير متاحة');
    return;
  }

  // إخفاء أزرار التعديل إذا لم تكن هناك صلاحية
  if (!permissionFunc('edit_deliver_custody')) {
    $('.edit-delivery-btn').hide();
  }

  // إخفاء أزرار الحذف إذا لم تكن هناك صلاحية
  if (!permissionFunc('delete_deliver_custody')) {
    $('.delete-delivery-btn').hide();
  }

  // تم حذف زر الطباعة
}

// إعداد فلاتر البحث المتقدمة لتسليم العهد
function setupAdvancedDeliverySearch() {
  const applyBtn = document.getElementById('applyAdvancedDeliverySearch');
  const clearBtn = document.getElementById('clearAdvancedDeliverySearch');

  if (applyBtn) {
    applyBtn.addEventListener('click', applyAdvancedDeliveryFilters);
  }

  if (clearBtn) {
    clearBtn.addEventListener('click', clearAdvancedDeliveryFilters);
  }

  // البحث التلقائي عند تغيير الحقول
  const searchInputs = [
    'searchDeliveryOperationNumber',
    'searchDeliveryEmployeeCode',
    'searchDeliveryEmployeeName',
    'searchDeliveryCustodyCode',
    'searchDeliveryStatus',
    'searchDeliveryDateFrom',
    'searchDeliveryDateTo'
  ];

  searchInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', debounce(applyAdvancedDeliveryFilters, 500));
      input.addEventListener('change', applyAdvancedDeliveryFilters);
    }
  });
}

// تطبيق الفلاتر المتقدمة لتسليم العهد
function applyAdvancedDeliveryFilters() {
  if (!window.deliveryDataTable) {
    return;
  }

  const filters = {
    operationNumber: document.getElementById('searchDeliveryOperationNumber')?.value || '',
    employeeCode: document.getElementById('searchDeliveryEmployeeCode')?.value || '',
    employeeName: document.getElementById('searchDeliveryEmployeeName')?.value || '',
    custodyCode: document.getElementById('searchDeliveryCustodyCode')?.value || '',
    status: document.getElementById('searchDeliveryStatus')?.value || '',
    dateFrom: document.getElementById('searchDeliveryDateFrom')?.value || '',
    dateTo: document.getElementById('searchDeliveryDateTo')?.value || ''
  };

  // تطبيق البحث على الأعمدة المحددة
  window.deliveryDataTable
    .column(0).search(filters.operationNumber)    // رقم العملية
    .column(1).search(filters.employeeCode)       // كود الموظف
    .column(2).search(filters.employeeName)       // اسم الموظف
    .column(4).search(filters.custodyCode)        // كود العهد
    .column(9).search(filters.status)             // الحالة
    .draw();

  // للتواريخ، سنحتاج لمعالجة خاصة في الخادم
  if (filters.dateFrom || filters.dateTo) {
    // إضافة معاملات التاريخ للطلب
    const settings = window.deliveryDataTable.settings()[0];
    settings.ajax.data = function(d) {
      d.dateFrom = filters.dateFrom;
      d.dateTo = filters.dateTo;
      return d;
    };
    window.deliveryDataTable.draw();
  }
}

// مسح الفلاتر المتقدمة لتسليم العهد
function clearAdvancedDeliveryFilters() {
  // مسح جميع حقول البحث
  document.getElementById('searchDeliveryOperationNumber').value = '';
  document.getElementById('searchDeliveryEmployeeCode').value = '';
  document.getElementById('searchDeliveryEmployeeName').value = '';
  document.getElementById('searchDeliveryCustodyCode').value = '';
  document.getElementById('searchDeliveryStatus').value = '';
  document.getElementById('searchDeliveryDateFrom').value = '';
  document.getElementById('searchDeliveryDateTo').value = '';

  // مسح الفلاتر من DataTable
  if (window.deliveryDataTable) {
    window.deliveryDataTable
      .columns().search('')
      .draw();

    // مسح معاملات التاريخ
    const settings = window.deliveryDataTable.settings()[0];
    settings.ajax.data = function(d) {
      return d;
    };
    window.deliveryDataTable.draw();
  }
}

// تحديث عرض سجلات التسليم (استبدال الدالة القديمة)
function displayDeliveryRecordsWithDataTables() {
  // التحقق من وجود التوكن
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('لا يوجد توكن مصادقة - لا يمكن تحديث البيانات');
    showNotification('خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.', 'error');
    return;
  }

  // إعادة تحميل بيانات DataTables
  if (window.deliveryDataTable) {
    window.deliveryDataTable.ajax.reload(null, false);
  } else {
    // إذا لم يكن DataTables مُهيأ بعد، انتظر قليلاً ثم حاول مرة أخرى
    setTimeout(() => {
      if (window.deliveryDataTable) {
        window.deliveryDataTable.ajax.reload(null, false);
      } else {
        // إذا لم يكن مُهيأ بعد، حاول تهيئته
        initializeDeliveryDataTables();
      }
    }, 1000);
  }
}

// إعداد فلاتر البحث المتقدمة للعهد
function setupAdvancedCustodySearch() {
  const applyBtn = document.getElementById('applyAdvancedCustodySearch');
  const clearBtn = document.getElementById('clearAdvancedCustodySearch');

  if (applyBtn) {
    applyBtn.addEventListener('click', applyAdvancedCustodyFilters);
  }

  if (clearBtn) {
    clearBtn.addEventListener('click', clearAdvancedCustodyFilters);
  }

  // البحث التلقائي عند تغيير الحقول
  const searchInputs = [
    'searchCustodyCode',
    'searchCustodyName',
    'searchCustodyType',
    'searchCustodyStatus',
    'searchDateFrom',
    'searchDateTo'
  ];

  searchInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('input', debounce(applyAdvancedCustodyFilters, 500));
      input.addEventListener('change', applyAdvancedCustodyFilters);
    }
  });
}

// تطبيق الفلاتر المتقدمة للعهد
function applyAdvancedCustodyFilters() {
  if (!window.custodyDataTable) {
    return;
  }

  const filters = {
    custodyCode: document.getElementById('searchCustodyCode')?.value || '',
    custodyName: document.getElementById('searchCustodyName')?.value || '',
    custodyType: document.getElementById('searchCustodyType')?.value || '',
    custodyStatus: document.getElementById('searchCustodyStatus')?.value || '',
    dateFrom: document.getElementById('searchDateFrom')?.value || '',
    dateTo: document.getElementById('searchDateTo')?.value || ''
  };

  // تطبيق البحث على الأعمدة المحددة
  window.custodyDataTable
    .column(0).search(filters.custodyCode)      // كود العهد
    .column(1).search(filters.custodyName)      // اسم العهدة
    .column(2).search(filters.custodyType)      // نوع العهدة
    .column(3).search(filters.custodyStatus)    // الحالة
    .draw();

  // للتواريخ، سنحتاج لمعالجة خاصة في الخادم
  if (filters.dateFrom || filters.dateTo) {
    // إضافة معاملات التاريخ للطلب
    const settings = window.custodyDataTable.settings()[0];
    settings.ajax.data = function(d) {
      d.dateFrom = filters.dateFrom;
      d.dateTo = filters.dateTo;
      return d;
    };
    window.custodyDataTable.draw();
  }
}

// مسح الفلاتر المتقدمة للعهد
function clearAdvancedCustodyFilters() {
  // مسح جميع حقول البحث
  document.getElementById('searchCustodyCode').value = '';
  document.getElementById('searchCustodyName').value = '';
  document.getElementById('searchCustodyType').value = '';
  document.getElementById('searchCustodyStatus').value = '';
  document.getElementById('searchDateFrom').value = '';
  document.getElementById('searchDateTo').value = '';

  // مسح الفلاتر من DataTable
  if (window.custodyDataTable) {
    window.custodyDataTable
      .columns().search('')
      .draw();

    // مسح معاملات التاريخ
    const settings = window.custodyDataTable.settings()[0];
    settings.ajax.data = function(d) {
      return d;
    };
    window.custodyDataTable.draw();
  }
}

// دالة مساعدة للتأخير (debounce)
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// تحديث دالة displayCustodyItems لاستخدام DataTables
function displayCustodyItemsWithDataTables() {
  // التحقق من وجود التوكن
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('لا يوجد توكن مصادقة - لا يمكن تحديث البيانات');
    showNotification('خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.', 'error');
    return;
  }

  // إعادة تحميل بيانات DataTables
  if (window.custodyDataTable) {
    window.custodyDataTable.ajax.reload(null, false); // false = عدم إعادة تعيين الصفحة
  } else {
    // إذا لم يكن DataTables مُهيأ بعد، انتظر قليلاً ثم حاول مرة أخرى
    setTimeout(() => {
      if (window.custodyDataTable) {
        window.custodyDataTable.ajax.reload(null, false);
      } else {
        // إذا لم يكن مُهيأ بعد، حاول تهيئته
        initializeCustodyDataTables();
      }
    }, 1000);
  }
}

// إعداد التحكم في جدول العهد
function setupCustodyTableControls(table) {
  try {
    // التحقق من وجود jQuery
    if (typeof $ === 'undefined') {
      console.log('jQuery غير متوفر');
      return;
    }

    // التحقق من وجود عنصر التحكم
    const entriesSelect = document.getElementById('custodyEntriesPerPage');
    const tableInfo = document.getElementById('custodyTableInfo');

    if (!entriesSelect || !tableInfo) {
      console.log('عناصر التحكم في جدول العهد غير موجودة');
      return;
    }

    // التحقق من وجود الجدول
    if (!table || typeof table.page !== 'function') {
      console.log('جدول العهد غير صالح');
      return;
    }

    // ربط خيار تحديد عدد الخانات
    $(entriesSelect).on('change', function() {
      try {
        const selectedValue = parseInt($(this).val());
        if (!isNaN(selectedValue) && selectedValue > 0) {
          table.page.len(selectedValue).draw();
          console.log('تم تغيير عدد خانات العهد إلى:', selectedValue);
        }
      } catch (error) {
        console.error('خطأ في تغيير عدد خانات العهد:', error);
      }
    });

    // تحديث معلومات الجدول
    function updateTableInfo() {
      try {
        if (!table || !table.page || !table.page.info) {
          return;
        }

        const info = table.page.info();
        const totalRecords = info.recordsTotal || 0;
        const displayedRecords = info.recordsDisplay || 0;
        const start = info.start + 1;
        const end = info.end;

        let infoText;
        if (totalRecords === 0) {
          infoText = 'لا توجد عهد للعرض';
        } else if (displayedRecords === totalRecords) {
          infoText = `عرض ${start} إلى ${end} من ${totalRecords} عهدة`;
        } else {
          infoText = `عرض ${start} إلى ${end} من ${displayedRecords} عهدة (مفلترة من ${totalRecords} عهدة)`;
        }

        $(tableInfo).text(infoText);
      } catch (error) {
        console.error('خطأ في تحديث معلومات جدول العهد:', error);
      }
    }

    // تحديث معلومات الجدول عند كل رسم
    table.on('draw', function() {
      updateTableInfo();
    });

    // تحديث أولي
    updateTableInfo();

  } catch (error) {
    console.error('خطأ في إعداد التحكم في جدول العهد:', error);
  }
}

// إعداد التحكم في جدول التسليم
function setupDeliveryTableControls(table) {
  try {
    // التحقق من وجود jQuery
    if (typeof $ === 'undefined') {
      console.log('jQuery غير متوفر');
      return;
    }

    // التحقق من وجود عنصر التحكم
    const entriesSelect = document.getElementById('deliveryEntriesPerPage');
    const tableInfo = document.getElementById('deliveryTableInfo');

    if (!entriesSelect || !tableInfo) {
      console.log('عناصر التحكم في جدول التسليم غير موجودة');
      return;
    }

    // التحقق من وجود الجدول
    if (!table || typeof table.page !== 'function') {
      console.log('جدول التسليم غير صالح');
      return;
    }

    // ربط خيار تحديد عدد الخانات
    $(entriesSelect).on('change', function() {
      try {
        const selectedValue = parseInt($(this).val());
        if (!isNaN(selectedValue) && selectedValue > 0) {
          table.page.len(selectedValue).draw();
          console.log('تم تغيير عدد خانات التسليم إلى:', selectedValue);
        }
      } catch (error) {
        console.error('خطأ في تغيير عدد خانات التسليم:', error);
      }
    });

    // تحديث معلومات الجدول
    function updateTableInfo() {
      try {
        if (!table || !table.page || !table.page.info) {
          return;
        }

        const info = table.page.info();
        const totalRecords = info.recordsTotal || 0;
        const displayedRecords = info.recordsDisplay || 0;
        const start = info.start + 1;
        const end = info.end;

        let infoText;
        if (totalRecords === 0) {
          infoText = 'لا توجد عمليات تسليم للعرض';
        } else if (displayedRecords === totalRecords) {
          infoText = `عرض ${start} إلى ${end} من ${totalRecords} عملية تسليم`;
        } else {
          infoText = `عرض ${start} إلى ${end} من ${displayedRecords} عملية تسليم (مفلترة من ${totalRecords} عملية تسليم)`;
        }

        $(tableInfo).text(infoText);
      } catch (error) {
        console.error('خطأ في تحديث معلومات جدول التسليم:', error);
      }
    }

    // تحديث معلومات الجدول عند كل رسم
    table.on('draw', function() {
      updateTableInfo();
    });

    // تحديث أولي
    updateTableInfo();

  } catch (error) {
    console.error('خطأ في إعداد التحكم في جدول التسليم:', error);
  }
}