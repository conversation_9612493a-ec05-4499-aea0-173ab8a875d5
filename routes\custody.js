const express = require('express');
const router = express.Router();
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');
const { logAction, createEditMessage, hasActualChanges } = require('../activityLogger');
const { enhanceSearchQuery } = require('../utils/textUtils');
const { addDepartmentFilter } = require('../middleware/departmentFilter');

/**
 * دالة لإضافة فلترة الإدارات لاستعلامات العهد
 * @param {string} whereClause - شرط WHERE الحالي
 * @param {Array} queryParams - معاملات الاستعلام الحالية
 * @param {Object} req - كائن الطلب
 * @returns {Object} - كائن يحتوي على whereClause و queryParams المحدثة
 */
function addDepartmentFilterToCustody(whereClause, queryParams, req) {
  console.log(`🔍 Custody Filter - allowedDepartments:`, req.allowedDepartments);

  if (req.allowedDepartments === null) {
    // المستخدم admin - لا نطبق فلترة
    console.log(`👑 Admin user - no filtering applied to custody`);
    return { whereClause, queryParams };
  } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
    // تطبيق فلترة الإدارات المسموحة - فلترة على الإدارة الحالية للموظف
    const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
    whereClause += ` AND e.department IN (${departmentPlaceholders}) AND e.department IS NOT NULL`;
    queryParams.push(...req.allowedDepartments);
    console.log(`✅ Custody filter applied for departments:`, req.allowedDepartments);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  } else {
    // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
    whereClause += ` AND 1 = 0`;
    console.log(`🚫 No departments allowed - blocking all custody data`);
    console.log(`📝 Final WHERE clause:`, whereClause);
    return { whereClause, queryParams };
  }
}

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  const jwt = require('jsonwebtoken');
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// الحصول على جميع العهد
router.get('/', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const [rows] = await pool.promise().query('SELECT * FROM custody ORDER BY created_at DESC, id DESC');
    res.json(rows);
  } catch (error) {
    console.error('Error fetching custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات العهد' });
  }
});

// إضافة عهدة جديدة
router.post('/', authenticateToken, async (req, res) => {
  const { custody_code, custody_name, custody_type, name, type, status, quantity, available_quantity, purchase_date, purchase_price, notes } = req.body;

  // دعم كلا من التنسيقين القديم والجديد
  const finalName = name || custody_name;
  const finalType = type || custody_type;

  if (!custody_code || !finalName || !finalType) {
    return res.status(400).json({ error: 'كود العهدة واسم العهدة ونوع العهدة مطلوبة' });
  }

  try {
    const pool = req.app.locals.pool;
    // التحقق من عدم وجود كود العهدة مسبقاً
    const [existingCode] = await pool.promise().query(
      'SELECT custody_code FROM custody WHERE custody_code = ?',
      [custody_code]
    );

    if (existingCode.length > 0) {
      return res.status(400).json({ error: 'كود العهدة موجود مسبقاً' });
    }

    // التحقق من عدم وجود اسم العهدة مسبقاً
    const [existingName] = await pool.promise().query(
      'SELECT name FROM custody WHERE name = ?',
      [finalName]
    );

    if (existingName.length > 0) {
      return res.status(409).json({
        error: 'اسم العهدة موجود مسبقاً. يرجى اختيار اسم مختلف.',
        duplicate_name: true,
        existing_name: finalName
      });
    }

    // إنشاء query ديناميكي بناءً على الحقول المتاحة (استخدام أسماء الأعمدة الصحيحة)
    let insertFields = ['custody_code', 'name', 'type'];
    let insertValues = [custody_code, finalName, finalType];
    let placeholders = ['?', '?', '?'];

    if (status) {
      insertFields.push('status');
      insertValues.push(status);
      placeholders.push('?');
    }

    if (quantity !== undefined) {
      insertFields.push('quantity');
      insertValues.push(quantity);
      placeholders.push('?');
    }

    if (available_quantity !== undefined) {
      insertFields.push('available_quantity');
      insertValues.push(available_quantity);
      placeholders.push('?');
    }

    if (purchase_date) {
      insertFields.push('purchase_date');
      insertValues.push(purchase_date);
      placeholders.push('?');
    }

    if (purchase_price !== undefined) {
      insertFields.push('purchase_price');
      insertValues.push(purchase_price);
      placeholders.push('?');
    }

    if (notes) {
      insertFields.push('notes');
      insertValues.push(notes);
      placeholders.push('?');
    }

    const [result] = await pool.promise().query(
      `INSERT INTO custody (${insertFields.join(', ')}) VALUES (${placeholders.join(', ')})`,
      insertValues
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'custody',
      record_id: result.insertId.toString(),
      message: `تم إضافة عهدة جديدة: ${finalName} - النوع: ${finalType} - الكمية: ${quantity || 1} - السعر: ${purchase_price || 'غير محدد'} جنيه`
    });

    res.status(201).json({
      message: 'تم إضافة العهدة بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('Error adding custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء إضافة العهدة' });
  }
});

// تحديث عهدة
router.put('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { name, type, status, quantity, custody_name, custody_type, purchase_date, purchase_price, notes } = req.body;

  // دعم كلا من التنسيقين القديم والجديد
  const finalName = name || custody_name;
  const finalType = type || custody_type;

  if (!finalName || !finalType) {
    return res.status(400).json({ error: 'اسم العهدة ونوع العهدة مطلوبان' });
  }

  try {
    const pool = req.app.locals.pool;

    // الحصول على البيانات القديمة قبل التحديث
    const [oldDataRows] = await pool.promise().query(
      'SELECT * FROM custody WHERE id = ?',
      [id]
    );

    if (oldDataRows.length === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    const oldData = oldDataRows[0];

    // إنشاء query ديناميكي بناءً على الحقول المتاحة
    let updateFields = [];
    let updateValues = [];

    if (finalName) {
      updateFields.push('name = ?');
      updateValues.push(finalName);
    }

    if (finalType) {
      updateFields.push('type = ?');
      updateValues.push(finalType);
    }

    if (quantity !== undefined) {
      updateFields.push('quantity = ?');
      updateValues.push(quantity);
    }

    if (status) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    if (purchase_date) {
      updateFields.push('purchase_date = ?');
      updateValues.push(purchase_date);
    }

    if (purchase_price !== undefined) {
      updateFields.push('purchase_price = ?');
      updateValues.push(purchase_price);
    }

    if (notes) {
      updateFields.push('notes = ?');
      updateValues.push(notes);
    }

    updateValues.push(id);

    const [result] = await pool.promise().query(
      `UPDATE custody SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    // إعداد البيانات الجديدة للمقارنة
    const newData = {
      name: finalName,
      type: finalType,
      status: status || oldData.status,
      quantity: quantity !== undefined ? quantity : oldData.quantity,
      purchase_date: purchase_date || oldData.purchase_date,
      purchase_price: purchase_price !== undefined ? purchase_price : oldData.purchase_price,
      notes: notes || oldData.notes
    };

    // التحقق من وجود تغييرات فعلية
    if (!hasActualChanges(oldData, newData)) {
      return res.status(400).json({ error: 'لا يوجد تغيير في البيانات' });
    }

    const fieldLabels = {
      name: 'اسم العهدة',
      type: 'نوع العهدة',
      status: 'الحالة',
      quantity: 'الكمية',
      purchase_date: 'تاريخ الشراء',
      purchase_price: 'سعر الشراء',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `عهدة: ${newData.name}`,
      oldData,
      newData,
      fieldLabels
    );

    // تحديث سجل التسليم تلقائياً إذا تم تغيير اسم أو نوع العهدة
    if ((finalName && finalName !== oldData.name) || (finalType && finalType !== oldData.type)) {
      await pool.promise().query(
        `UPDATE custody_delivery
         SET custody_name = ?, custody_type = ?
         WHERE custody_code = ?`,
        [finalName || oldData.name, finalType || oldData.type, oldData.custody_code]
      );

      console.log(`تم تحديث سجل التسليم للعهدة ${oldData.custody_code} - الاسم الجديد: ${finalName || oldData.name}, النوع الجديد: ${finalType || oldData.type}`);
    }

    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'custody',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({
      message: 'تم تحديث العهدة بنجاح',
      delivery_updated: (finalName && finalName !== oldData.name) || (finalType && finalType !== oldData.type)
    });
  } catch (error) {
    console.error('Error updating custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء تحديث العهدة' });
  }
});

// حذف عهدة
router.delete('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const pool = req.app.locals.pool;
    // أولاً، الحصول على بيانات العهدة كاملة من جدول custody
    const [custodyData] = await pool.promise().query(
      'SELECT * FROM custody WHERE id = ?',
      [id]
    );

    if (custodyData.length === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    const custody = custodyData[0];
    const custodyCode = custody.custody_code;

    // التحقق من عدم وجود تسليمات مرتبطة بهذه العهدة
    const [deliveries] = await pool.promise().query(
      'SELECT id FROM custody_delivery WHERE custody_code = ?',
      [custodyCode]
    );

    if (deliveries.length > 0) {
      return res.status(400).json({
        error: `لا يمكن حذف هذه العهدة لأنها مسلمة لـ ${deliveries.length} موظف. يجب استرجاع العهدة أولاً قبل الحذف.`
      });
    }

    const [result] = await pool.promise().query('DELETE FROM custody WHERE id = ?', [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'custody',
      record_id: id.toString(),
      message: `تم حذف عهدة: ${custody.name} - النوع: ${custody.type} - الكود: ${custody.custody_code} - الكمية: ${custody.quantity || 1} - السعر: ${custody.purchase_price || 'غير محدد'} جنيه`
    });

    res.json({ message: 'تم حذف العهدة بنجاح' });
  } catch (error) {
    console.error('Error deleting custody:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      sql: error.sql,
      sqlMessage: error.sqlMessage
    });
    res.status(500).json({
      error: 'حدث خطأ أثناء حذف العهدة',
      details: error.message
    });
  }
});

// الحصول على جميع سجلات تسليم العهد
router.get('/delivery', authenticateToken, addDepartmentFilter, async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    // بناء الاستعلام مع فلترة الإدارات
    let query, params;

    if (req.allowedDepartments === null) {
      // المستخدم admin - جلب جميع سجلات التسليم
      query = `
        SELECT cd.*, e.full_name as employee_name, e.department
        FROM custody_delivery cd
        LEFT JOIN employees e ON cd.employee_code = e.code
        WHERE cd.status = "مسلم"
        ORDER BY cd.created_at DESC, cd.id DESC
      `;
      params = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // المستخدم محدود - فلترة حسب الإدارات المسموحة
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      query = `
        SELECT cd.*, e.full_name as employee_name, e.department
        FROM custody_delivery cd
        INNER JOIN employees e ON cd.employee_code = e.code
        WHERE cd.status = "مسلم"
          AND e.department IN (${departmentPlaceholders})
          AND e.department IS NOT NULL
        ORDER BY cd.created_at DESC, cd.id DESC
      `;
      params = req.allowedDepartments;
    } else {
      // لا يوجد إدارات مسموحة
      query = `SELECT * FROM custody_delivery WHERE 1 = 0`;
      params = [];
    }

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching custody delivery:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات تسليم العهد' });
  }
});

// الحصول على جميع سجلات التسليم للحساب (بدون فلترة الإدارات)
router.get('/delivery/all-for-calculation', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    // جلب جميع سجلات التسليم بدون فلترة الإدارات لحساب الكميات الصحيحة
    const query = `
      SELECT custody_code, quantity, status
      FROM custody_delivery
      WHERE status = "مسلم"
    `;

    const [rows] = await pool.promise().query(query);
    console.log(`📊 Fetched ${rows.length} delivery records for quantity calculation`);
    res.json(rows);
  } catch (error) {
    console.error('Error fetching delivery records for calculation:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب سجلات التسليم للحساب' });
  }
});

// جلب أرقام العمليات لتوليد الرقم التالي
router.get('/delivery/operation-numbers', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const [rows] = await pool.promise().query('SELECT operation_number FROM custody_delivery ORDER BY operation_number DESC');
    res.json(rows);
  } catch (error) {
    console.error('Error fetching operation numbers:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب أرقام العمليات' });
  }
});

// تسليم عهدة لموظف
router.post('/delivery', authenticateToken, async (req, res) => {
  const {
    operation_number,
    custody_code,
    custody_name,
    custody_type,
    employee_code,
    employee_name,
    department,
    quantity,
    delivery_date,
    status,
    notes
  } = req.body;

  if (!operation_number || !custody_code || !employee_code || !delivery_date || !quantity) {
    return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب ملؤها' });
  }

  // تحويل employee_code إلى رقم صحيح للتوافق مع قاعدة البيانات
  const employeeCodeInt = parseInt(employee_code);
  if (isNaN(employeeCodeInt)) {
    return res.status(400).json({ error: 'كود الموظف يجب أن يكون رقماً صحيحاً' });
  }

  try {
    const pool = req.app.locals.pool;
    // التحقق من عدم وجود رقم العملية مسبقاً
    const [existing] = await pool.promise().query(
      'SELECT operation_number FROM custody_delivery WHERE operation_number = ?',
      [operation_number]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: 'رقم العملية موجود مسبقاً' });
    }

    // التحقق من الكمية المتاحة للتسليم
    // حساب الكمية المسلمة حالياً
    const [deliveredQtyResult] = await pool.promise().query(
      'SELECT COALESCE(SUM(quantity), 0) as delivered_qty FROM custody_delivery WHERE custody_code = ? AND status = "مسلم"',
      [custody_code]
    );

    const deliveredQty = deliveredQtyResult[0].delivered_qty || 0;

    // الحصول على الكمية الإجمالية للعهدة
    const [custodyResult] = await pool.promise().query(
      'SELECT quantity FROM custody WHERE custody_code = ?',
      [custody_code]
    );

    if (custodyResult.length === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    const totalQty = custodyResult[0].quantity;
    const availableQty = totalQty - deliveredQty;

    // التحقق من توفر الكمية المطلوبة
    if (quantity > availableQty) {
      return res.status(400).json({
        error: `الكمية المطلوبة (${quantity}) أكبر من الكمية المتاحة (${availableQty})`
      });
    }

    const [result] = await pool.promise().query(
      `INSERT INTO custody_delivery
       (operation_number, custody_code, custody_name, custody_type, employee_code, employee_name, department, quantity, delivery_date, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [operation_number, custody_code, custody_name, custody_type, employeeCodeInt, employee_name, department, quantity, delivery_date, status || 'مسلم']
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'deliver',
      module: 'custody',
      record_id: result.insertId.toString(),
      message: `تم تسليم عهدة: ${custody_name} (كود: ${custody_code}) للموظف: ${employee_name} (كود: ${employee_code}) - القسم: ${department} - الكمية: ${quantity} - رقم العملية: ${operation_number}`
    });

    res.status(201).json({
      message: 'تم تسليم العهدة بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('Error delivering custody:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      sql: error.sql,
      sqlMessage: error.sqlMessage
    });
    res.status(500).json({
      error: 'حدث خطأ أثناء تسليم العهدة',
      details: error.message
    });
  }
});

// تعديل سجل تسليم العهد
router.put('/delivery/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const {
    employee_code,
    employee_name,
    department,
    custody_code,
    custody_name,
    custody_type,
    quantity,
    delivery_date,
    status
  } = req.body;

  // التحقق من البيانات المطلوبة
  if (!employee_code || !employee_name || !custody_code || !delivery_date || !quantity) {
    return res.status(400).json({ error: 'جميع البيانات المطلوبة يجب أن تكون موجودة' });
  }

  // تحويل employee_code إلى رقم صحيح للتوافق مع قاعدة البيانات
  const employeeCodeInt = parseInt(employee_code);
  if (isNaN(employeeCodeInt)) {
    return res.status(400).json({ error: 'كود الموظف يجب أن يكون رقماً صحيحاً' });
  }

  // التحقق من صحة الكمية
  const quantityInt = parseInt(quantity);
  if (isNaN(quantityInt) || quantityInt <= 0) {
    return res.status(400).json({ error: 'الكمية يجب أن تكون رقماً صحيحاً أكبر من صفر' });
  }

  try {
    const pool = req.app.locals.pool;

    // الحصول على البيانات الأصلية قبل التحديث
    const [originalData] = await pool.promise().query(
      'SELECT * FROM custody_delivery WHERE id = ?',
      [id]
    );

    if (originalData.length === 0) {
      return res.status(404).json({ error: 'سجل التسليم غير موجود' });
    }

    const original = originalData[0];

    // إعداد البيانات الجديدة للمقارنة
    const newData = {
      employee_code: employeeCodeInt,
      employee_name,
      department,
      custody_code,
      custody_name,
      custody_type,
      quantity: quantityInt,
      delivery_date: (delivery_date === '' || delivery_date === null || delivery_date === undefined) ? null : delivery_date,
      status: status || original.status
    };

    // التحقق من وجود تغييرات فعلية
    if (!hasActualChanges(original, newData)) {
      return res.status(400).json({ error: 'لا يوجد تغيير في البيانات' });
    }

    // التحقق من الكمية المتاحة عند تغيير الكمية أو كود العهدة
    if (quantityInt !== parseInt(original.quantity) || custody_code !== original.custody_code) {
      // الحصول على الكمية الإجمالية وحالة العهدة
      const [custodyResult] = await pool.promise().query(
        'SELECT quantity, status FROM custody WHERE custody_code = ?',
        [custody_code]
      );

      if (custodyResult.length === 0) {
        return res.status(404).json({ error: 'العهدة غير موجودة' });
      }

      const totalQty = custodyResult[0].quantity;
      const custodyStatus = custodyResult[0].status;

      // التحقق من حالة العهدة
      if (custodyStatus === 'صيانة') {
        return res.status(400).json({ error: 'لا يمكن تسليم عهدة في حالة صيانة' });
      }

      // حساب الكمية المسلمة حالياً (باستثناء السجل الحالي)
      const [deliveredQtyResult] = await pool.promise().query(
        'SELECT COALESCE(SUM(quantity), 0) as delivered_qty FROM custody_delivery WHERE custody_code = ? AND status = "مسلم" AND id != ?',
        [custody_code, id]
      );

      const deliveredQty = deliveredQtyResult[0].delivered_qty || 0;
      const availableQty = totalQty - deliveredQty;

      // التحقق من توفر الكمية المطلوبة
      if (quantityInt > availableQty) {
        return res.status(400).json({
          error: `❌ الكمية المطلوبة (${quantityInt}) أكبر من الكمية المتاحة!\n\n📊 تفاصيل المخزون:\n• الكمية الإجمالية: ${totalQty}\n• المسلم حالياً: ${deliveredQty}\n• المتاح للتسليم: ${availableQty}\n\n💡 يرجى تعديل الكمية لتكون ${availableQty} أو أقل.`
        });
      }
    }

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    const processedDeliveryDate = (delivery_date === '' || delivery_date === null || delivery_date === undefined) ? null : delivery_date;

    // تحديث سجل التسليم
    const [result] = await pool.promise().query(
      `UPDATE custody_delivery SET
        employee_code = ?,
        employee_name = ?,
        department = ?,
        custody_code = ?,
        custody_name = ?,
        custody_type = ?,
        quantity = ?,
        delivery_date = ?,
        status = ?
      WHERE id = ?`,
      [employeeCodeInt, employee_name, department, custody_code, custody_name, custody_type, quantityInt, processedDeliveryDate, status, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'سجل التسليم غير موجود' });
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'custody_delivery',
      record_id: id.toString(),
      message: `تم تعديل سجل تسليم عهدة: ${custody_name} (كود: ${custody_code}) للموظف: ${employee_name} (كود: ${employeeCodeInt}) - القسم: ${department} - الكمية: ${quantityInt} - رقم العملية: ${original.operation_number}`
    });

    res.json({
      success: true,
      message: 'تم تحديث سجل التسليم بنجاح',
      id: parseInt(id)
    });
  } catch (error) {
    console.error('Error updating delivery record:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء تحديث سجل التسليم' });
  }
});

// استرجاع عهدة من موظف
router.put('/delivery/:id/return', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { return_date, return_notes } = req.body;

  if (!return_date) {
    return res.status(400).json({ error: 'تاريخ الاسترجاع مطلوب' });
  }

  try {
    const pool = req.app.locals.pool;

    // الحصول على بيانات التسليم قبل التحديث
    const [deliveryData] = await pool.promise().query(
      'SELECT * FROM custody_delivery WHERE id = ?',
      [id]
    );

    if (deliveryData.length === 0) {
      return res.status(404).json({ error: 'سجل التسليم غير موجود' });
    }

    const delivery = deliveryData[0];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    const processedReturnDate = (return_date === '' || return_date === null || return_date === undefined) ? null : return_date;

    const [result] = await pool.promise().query(
      'UPDATE custody_delivery SET status = "مسترجع", return_date = ?, return_notes = ? WHERE id = ?',
      [processedReturnDate, return_notes, id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'return',
      module: 'custody',
      record_id: id.toString(),
      message: `تم استرجاع عهدة: ${delivery.custody_name} (كود: ${delivery.custody_code}) من الموظف: ${delivery.employee_name} (كود: ${delivery.employee_code}) - تاريخ الاسترجاع: ${return_date} - رقم العملية: ${delivery.operation_number}`
    });

    res.json({ message: 'تم استرجاع العهدة بنجاح' });
  } catch (error) {
    console.error('Error returning custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء استرجاع العهدة' });
  }
});

// حذف سجل تسليم عهدة
router.delete('/delivery/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const pool = req.app.locals.pool;

    // الحصول على بيانات السجل قبل الحذف
    const [deliveryData] = await pool.promise().query(
      'SELECT * FROM custody_delivery WHERE id = ?',
      [id]
    );

    if (deliveryData.length === 0) {
      return res.status(404).json({ error: 'سجل التسليم غير موجود' });
    }

    const delivery = deliveryData[0];

    // حذف السجل
    const [result] = await pool.promise().query('DELETE FROM custody_delivery WHERE id = ?', [id]);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'custody_delivery',
      record_id: id.toString(),
      message: `تم حذف سجل تسليم عهدة: ${delivery.custody_name} (كود: ${delivery.custody_code}) للموظف: ${delivery.employee_name} (كود: ${delivery.employee_code}) - القسم: ${delivery.department} - الكمية: ${delivery.quantity} - رقم العملية: ${delivery.operation_number}`
    });

    res.json({ message: 'تم حذف سجل التسليم بنجاح' });
  } catch (error) {
    console.error('Error deleting custody delivery:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء حذف سجل التسليم' });
  }
});

// DataTables server-side processing للعهد
router.get('/datatables', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';
    const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
    const orderDirection = req.query.order?.[0]?.dir || 'desc';

    // أعمدة الجدول (حسب ترتيب الظهور في HTML)
    const columns = [
      'custody_code',
      'name',
      'type',
      'status',
      'quantity',
      'available_quantity', // الكمية المصروفة
      'available_quantity', // الكمية المتبقية
      'created_at'
    ];

    const orderColumn = columns[orderColumnIndex] || 'created_at';

    // بناء استعلام البحث
    let whereClause = '';
    let searchParams = [];

    if (searchValue) {
      whereClause = `WHERE (
        custody_code LIKE ? OR
        name LIKE ? OR
        type LIKE ? OR
        status LIKE ?
      )`;
      const searchPattern = `%${searchValue}%`;
      searchParams = [searchPattern, searchPattern, searchPattern, searchPattern];
    }

    // البحث في الأعمدة المحددة (للفلاتر المتقدمة)
    const columnSearches = [];
    const columnParams = [];

    // البحث بكود العهد
    if (req.query.columns?.[0]?.search?.value) {
      columnSearches.push('custody_code LIKE ?');
      columnParams.push(`%${req.query.columns[0].search.value}%`);
    }

    // البحث باسم العهدة
    if (req.query.columns?.[1]?.search?.value) {
      columnSearches.push('name LIKE ?');
      columnParams.push(`%${req.query.columns[1].search.value}%`);
    }

    // البحث بنوع العهدة
    if (req.query.columns?.[2]?.search?.value) {
      columnSearches.push('type LIKE ?');
      columnParams.push(`%${req.query.columns[2].search.value}%`);
    }

    // البحث بالحالة
    if (req.query.columns?.[3]?.search?.value) {
      columnSearches.push('status LIKE ?');
      columnParams.push(`%${req.query.columns[3].search.value}%`);
    }

    // فلاتر التاريخ
    if (req.query.dateFrom) {
      columnSearches.push('DATE(created_at) >= ?');
      columnParams.push(req.query.dateFrom);
    }

    if (req.query.dateTo) {
      columnSearches.push('DATE(created_at) <= ?');
      columnParams.push(req.query.dateTo);
    }

    // دمج شروط البحث
    if (columnSearches.length > 0) {
      if (whereClause) {
        whereClause += ' AND (' + columnSearches.join(' AND ') + ')';
        searchParams = searchParams.concat(columnParams);
      } else {
        whereClause = 'WHERE ' + columnSearches.join(' AND ');
        searchParams = columnParams;
      }
    }

    // حساب إجمالي السجلات
    const [totalResult] = await pool.promise().query('SELECT COUNT(*) as total FROM custody');
    const totalRecords = totalResult[0].total;

    // حساب السجلات المفلترة
    const [filteredResult] = await pool.promise().query(
      `SELECT COUNT(*) as total FROM custody ${whereClause}`,
      searchParams
    );
    const filteredRecords = filteredResult[0].total;

    // جلب البيانات مع الترقيم والترتيب
    const [rows] = await pool.promise().query(
      `SELECT custody_code, name, type, status, quantity, available_quantity,
              DATE_FORMAT(created_at, '%Y-%m-%d') as created_at_formatted,
              id
       FROM custody
       ${whereClause}
       ORDER BY ${orderColumn} ${orderDirection}
       LIMIT ? OFFSET ?`,
      [...searchParams, length, start]
    );

    // لا نحتاج لجلب تفاصيل المستلمين بعد الآن

    // حساب الكمية المصروفة لكل عهدة
    const custodyDeliveryQuery = `
      SELECT custody_code, SUM(quantity) as delivered_quantity
      FROM custody_delivery
      WHERE status = 'مسلم'
      GROUP BY custody_code
    `;
    const [deliveryRows] = await pool.promise().query(custodyDeliveryQuery);

    // إنشاء خريطة للكميات المصروفة
    const deliveredQuantities = {};
    deliveryRows.forEach(row => {
      deliveredQuantities[row.custody_code] = parseInt(row.delivered_quantity) || 0;
    });

    // تنسيق البيانات للعرض
    const formattedData = rows.map(row => {
      const deliveredQty = deliveredQuantities[row.custody_code] || 0;
      const remainingQty = row.quantity - deliveredQty;

      // تحديد لون الحالة
      let statusClass = '';
      switch(row.status) {
        case 'جديدة':
          statusClass = 'status-new';
          break;
        case 'مستعمل':
          statusClass = 'status-used';
          break;
        case 'صيانة':
          statusClass = 'status-maintenance';
          break;
        default:
          statusClass = 'status-default';
      }

      return [
        row.custody_code,
        row.name,
        row.type,
        `<span class="${statusClass}">${row.status}</span>`,
        row.quantity,
        `<span style="color: #dc3545; font-weight: bold;">${deliveredQty}</span>`, // أحمر للكمية المصروفة
        `<span style="color: #28a745; font-weight: bold;">${remainingQty}</span>`, // أخضر للكمية المتبقية
        row.created_at_formatted,
        `<button class="edit-custody-btn" data-custody-id="${row.id}" data-permission="can_edit">تعديل</button>
         <button class="delete-custody-btn" data-custody-id="${row.id}" data-permission="can_delete">حذف</button>
         <button class="details-custody-btn" data-custody-code="${row.custody_code}" data-custody-name="${row.name}">تفاصيل المستلم</button>`
      ];
    });

    // إرجاع البيانات بتنسيق DataTables
    res.json({
      draw: draw,
      recordsTotal: totalRecords,
      recordsFiltered: filteredRecords,
      data: formattedData
    });

  } catch (error) {
    console.error('خطأ في جلب بيانات DataTables للعهد:', error);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      draw: req.query.draw || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: []
    });
  }
});

// DataTables server-side processing لسجل تسليم العهد
router.get('/delivery/datatables', authenticateToken, addDepartmentFilter, async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    // معاملات DataTables
    const draw = parseInt(req.query.draw) || 1;
    const start = parseInt(req.query.start) || 0;
    const length = parseInt(req.query.length) || 10;
    const searchValue = req.query.search?.value || '';
    const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
    const orderDirection = req.query.order?.[0]?.dir || 'desc';

    // أعمدة الجدول (حسب ترتيب الظهور في HTML)
    const columns = [
      'operation_number',
      'employee_code',
      'employee_name',
      'department',
      'custody_code',
      'custody_name',
      'custody_type',
      'quantity',
      'delivery_date',
      'status',
      'id' // للترتيب حسب الإدخال (الأحدث أولاً)
    ];

    const orderColumn = columns[orderColumnIndex] || 'delivery_date';

    // بناء استعلام البحث
    let whereClause = '';
    let searchParams = [];

    if (searchValue) {
      whereClause = `WHERE (
        operation_number LIKE ? OR
        employee_code LIKE ? OR
        employee_name LIKE ? OR
        department LIKE ? OR
        custody_code LIKE ? OR
        custody_name LIKE ? OR
        custody_type LIKE ? OR
        status LIKE ?
      )`;
      const searchPattern = `%${searchValue}%`;
      searchParams = [searchPattern, searchPattern, searchPattern, searchPattern,
                     searchPattern, searchPattern, searchPattern, searchPattern];
    }

    // البحث في الأعمدة المحددة (للفلاتر المتقدمة)
    const columnSearches = [];
    const columnParams = [];

    // البحث برقم العملية
    if (req.query.columns?.[0]?.search?.value) {
      columnSearches.push('operation_number LIKE ?');
      columnParams.push(`%${req.query.columns[0].search.value}%`);
    }

    // البحث بكود الموظف
    if (req.query.columns?.[1]?.search?.value) {
      columnSearches.push('employee_code LIKE ?');
      columnParams.push(`%${req.query.columns[1].search.value}%`);
    }

    // البحث باسم الموظف
    if (req.query.columns?.[2]?.search?.value) {
      columnSearches.push('employee_name LIKE ?');
      columnParams.push(`%${req.query.columns[2].search.value}%`);
    }

    // البحث بكود العهد
    if (req.query.columns?.[4]?.search?.value) {
      columnSearches.push('custody_code LIKE ?');
      columnParams.push(`%${req.query.columns[4].search.value}%`);
    }

    // البحث بالحالة
    if (req.query.columns?.[9]?.search?.value) {
      columnSearches.push('status LIKE ?');
      columnParams.push(`%${req.query.columns[9].search.value}%`);
    }

    // فلاتر التاريخ
    if (req.query.dateFrom) {
      columnSearches.push('DATE(delivery_date) >= ?');
      columnParams.push(req.query.dateFrom);
    }

    if (req.query.dateTo) {
      columnSearches.push('DATE(delivery_date) <= ?');
      columnParams.push(req.query.dateTo);
    }

    // دمج شروط البحث
    if (columnSearches.length > 0) {
      if (whereClause) {
        whereClause += ' AND (' + columnSearches.join(' AND ') + ')';
        searchParams = searchParams.concat(columnParams);
      } else {
        whereClause = 'WHERE ' + columnSearches.join(' AND ');
        searchParams = columnParams;
      }
    }

    // إضافة فلترة الإدارات للاستعلام
    let departmentWhereClause = whereClause || 'WHERE 1=1';
    let departmentParams = [...searchParams];

    // تطبيق فلترة الإدارات
    if (req.allowedDepartments === null) {
      // المستخدم admin - لا نطبق فلترة إضافية
      console.log(`👑 Admin user - no department filtering applied to custody delivery DataTables`);
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      // تطبيق فلترة الإدارات المسموحة
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      if (departmentWhereClause.includes('WHERE')) {
        departmentWhereClause += ` AND department IN (${departmentPlaceholders}) AND department IS NOT NULL`;
      } else {
        departmentWhereClause = `WHERE department IN (${departmentPlaceholders}) AND department IS NOT NULL`;
      }
      departmentParams.push(...req.allowedDepartments);
      console.log(`✅ Custody delivery DataTables filter applied for departments:`, req.allowedDepartments);
    } else {
      // لا يوجد إدارات مسموحة - لا يعرض أي نتائج
      if (departmentWhereClause.includes('WHERE')) {
        departmentWhereClause += ` AND 1 = 0`;
      } else {
        departmentWhereClause = `WHERE 1 = 0`;
      }
      console.log(`🚫 No departments allowed - blocking all custody delivery data in DataTables`);
    }

    // حساب إجمالي السجلات مع فلترة الإدارات
    let totalCountWhereClause = 'WHERE 1=1';
    let totalCountParams = [];

    if (req.allowedDepartments === null) {
      // المستخدم admin - عد جميع السجلات
      totalCountWhereClause = '';
      totalCountParams = [];
    } else if (req.allowedDepartments && req.allowedDepartments.length > 0) {
      const departmentPlaceholders = req.allowedDepartments.map(() => '?').join(',');
      totalCountWhereClause = `WHERE department IN (${departmentPlaceholders}) AND department IS NOT NULL`;
      totalCountParams = [...req.allowedDepartments];
    } else {
      totalCountWhereClause = 'WHERE 1 = 0';
      totalCountParams = [];
    }

    const [totalResult] = await pool.promise().query(
      `SELECT COUNT(*) as total FROM custody_delivery ${totalCountWhereClause}`,
      totalCountParams
    );
    const totalRecords = totalResult[0].total;

    // حساب السجلات المفلترة
    const [filteredResult] = await pool.promise().query(
      `SELECT COUNT(*) as total FROM custody_delivery ${departmentWhereClause}`,
      departmentParams
    );
    const filteredRecords = filteredResult[0].total;

    // جلب البيانات مع الترقيم والترتيب
    const [rows] = await pool.promise().query(
      `SELECT operation_number, employee_code, employee_name, department,
              custody_code, custody_name, custody_type, quantity,
              DATE_FORMAT(delivery_date, '%Y-%m-%d') as delivery_date_formatted,
              status, id
       FROM custody_delivery
       ${departmentWhereClause}
       ORDER BY ${orderColumn} ${orderDirection}
       LIMIT ? OFFSET ?`,
      [...departmentParams, length, start]
    );

    // تنسيق البيانات للعرض
    const formattedData = rows.map(row => {
      // تحديد لون الحالة
      let statusClass = '';
      switch(row.status) {
        case 'مسلم':
          statusClass = 'status-delivered';
          break;
        case 'مرتجع':
          statusClass = 'status-returned';
          break;
        default:
          statusClass = 'status-default';
      }

      return [
        row.operation_number || 'غير محدد',
        row.employee_code,
        row.employee_name,
        row.department || 'غير محدد',
        row.custody_code,
        row.custody_name,
        row.custody_type,
        `<span style="color: #007bff; font-weight: bold;">${row.quantity}</span>`,
        row.delivery_date_formatted,
        `<span class="${statusClass}">${row.status}</span>`,
        row.id, // عمود مخفي للترتيب (الأحدث أولاً)
        `<button class="edit-delivery-btn" data-delivery-id="${row.id}" data-permission="can_edit">تعديل</button>
         <button class="delete-delivery-btn" data-delivery-id="${row.id}" data-permission="can_delete">حذف</button>`
      ];
    });

    // إرجاع البيانات بتنسيق DataTables
    res.json({
      draw: draw,
      recordsTotal: totalRecords,
      recordsFiltered: filteredRecords,
      data: formattedData
    });

  } catch (error) {
    console.error('خطأ في جلب بيانات DataTables لتسليم العهد:', error);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      draw: req.query.draw || 1,
      recordsTotal: 0,
      recordsFiltered: 0,
      data: []
    });
  }
});

// الحصول على العهد المسلمة لموظف معين
router.get('/employee/:employeeCode', async (req, res) => {
  const { employeeCode } = req.params;

  try {
    const pool = req.app.locals.pool;
    const [rows] = await pool.promise().query(
      'SELECT * FROM custody_delivery WHERE employee_code = ? AND status = "مسلم" ORDER BY created_at DESC, id DESC',
      [employeeCode]
    );
    res.json(rows);
  } catch (error) {
    console.error('Error fetching employee custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب عهد الموظف' });
  }
});

// البحث في العهد
router.get('/search', async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    const {
      custody_code,
      name,
      type,
      status,
      min_price,
      max_price,
      start_date,
      end_date
    } = req.query;

    let query = `
      SELECT * FROM custody
      WHERE 1=1
    `;
    const params = [];

    // البحث بكود العهدة
    if (custody_code) {
      query += " AND custody_code = ?";
      params.push(custody_code);
    }

    // البحث باسم العهدة مع التطبيع العربي
    if (name) {
      const searchColumns = ['name'];
      const enhanced = enhanceSearchQuery('SELECT 1', name, searchColumns, []);
      const searchCondition = enhanced.query.replace('SELECT 1 WHERE ', '');
      query += ` AND ${searchCondition}`;
      params.push(...enhanced.params);
    }

    // البحث بنوع العهدة
    if (type) {
      query += " AND type = ?";
      params.push(type);
    }

    // البحث بالحالة
    if (status) {
      query += " AND status = ?";
      params.push(status);
    }

    // البحث بنطاق السعر
    if (min_price) {
      query += " AND purchase_price >= ?";
      params.push(min_price);
    }

    if (max_price) {
      query += " AND purchase_price <= ?";
      params.push(max_price);
    }

    // البحث بنطاق التاريخ
    if (start_date) {
      query += " AND purchase_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND purchase_date <= ?";
      params.push(end_date);
    }

    query += " ORDER BY created_at DESC, id DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في العهد:', error);
    res.status(500).json({ error: 'فشل في البحث في العهد' });
  }
});

// البحث في سجلات التسليم
router.get('/delivery/search', async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    const {
      employee_code,
      employee_name,
      custody_type,
      start_date,
      end_date
    } = req.query;

    let query = `
      SELECT * FROM custody_delivery
      WHERE 1=1
    `;
    const params = [];

    // البحث بكود الموظف
    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }

    // البحث باسم الموظف
    if (employee_name) {
      query += " AND employee_name LIKE ?";
      params.push(`%${employee_name}%`);
    }

    // البحث بنوع العهدة
    if (custody_type) {
      query += " AND custody_type = ?";
      params.push(custody_type);
    }

    // البحث بنطاق التاريخ
    if (start_date) {
      query += " AND delivery_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND delivery_date <= ?";
      params.push(end_date);
    }

    query += " ORDER BY delivery_date DESC, id DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في سجلات التسليم:', error);
    res.status(500).json({ error: 'فشل في البحث في سجلات التسليم' });
  }
});

module.exports = router;
