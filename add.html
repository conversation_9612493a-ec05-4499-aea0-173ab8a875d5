<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>إضافة موظف جديد</title>

  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="add.css">
  <style>
    .form-text.text-muted {
      font-size: 0.875rem;
      color: #6c757d;
      margin-top: 0.25rem;
      display: block;
    }
  </style>
  <link rel="stylesheet" href="no-sidebar-layout.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">



  <script src="permissions.js" defer></script>
</head>
<body class="add-page">

  <!-- زر العودة للوحة التحكم -->
  <div class="back-to-dashboard">
    <a href="dashboard.html" class="dashboard-btn">
      <i class="fas fa-home"></i>
      <span>العودة للوحة التحكم</span>
    </a>
  </div>

  <!-- المحتوى الرئيسي -->
  <div class="main-content full-width" id="mainContent">
    <h1 class="form-title">إضافة موظف جديد</h1>
    <form id="addEmployeeForm" class="modern-form">
      <div class="form-grid">
        <div class="form-group">
          <label>كود الموظف</label>
          <input type="text" name="code" id="employeeCode" readonly required style="background-color: #f8f9fa; cursor: not-allowed;" />
        </div>
        <div class="form-group">
          <label>الاسم الكامل</label>
          <input type="text" name="full_name" required />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>الإدارة</label>
          <select name="department" id="departmentSelect" required>
            <option value="">اختر الإدارة</option>
          </select>
        </div>
        <div class="form-group">
          <label>الوظيفة</label>
          <input type="text" name="job_title" />
        </div>
        <div class="form-group">
          <label>تاريخ التعيين <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="hire_date" />
        </div>
        <div class="form-group">
          <label>تاريخ بداية عقد العمل <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="contract_start_date" />
        </div>
        <div class="form-group">
          <label>تاريخ انتهاء عقد العمل <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="contract_end_date" id="contract_end_date" />
        </div>
        <div class="form-group">
          <label>المدة المتبقية من العقد <i class="fa-regular fa-clock"></i></label>
          <input type="text" id="contract_remaining" readonly placeholder="سيتم الحساب تلقائياً"
                 style="background-color: #f8f9fa; color: #6c757d; cursor: not-allowed;" />
          <small class="form-text text-muted">حقل استرشادي - يحسب المدة المتبقية من اليوم حتى انتهاء العقد</small>
        </div>
        <div class="form-group">
          <label>العنوان</label>
          <input type="text" name="address" />
        </div>
        <div class="form-group">
          <label>المؤهل</label>
          <input type="text" name="qualification" />
        </div>
        <div class="form-group">
          <label>التليفون</label>
          <input type="text" name="phone" />
        </div>
        <div class="form-group">
          <label>تاريخ الميلاد <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="birth_date" />
        </div>
        <div class="form-group">
          <label>الحالة الاجتماعية</label>
          <input type="text" name="marital_status" />
        </div>
        <div class="form-group">
          <label>عدد الأبناء</label>
          <input type="text" name="children" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>الرقم القومي</label>
          <input type="text" name="national_id" />
        </div>
        <div class="form-group">
          <label>التأمين التكافلي</label>
          <input type="text" name="social_insurance" />
        </div>
        <div class="form-group">
          <label>الرقم التأميني</label>
          <input type="text" name="insurance_number" />
        </div>
        <div class="form-group">
          <label>جهة التأمين</label>
          <input type="text" name="insurance_entity" />
        </div>
        <div class="form-group">
          <label>تاريخ التأمين عليه <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="insurance_start" />
        </div>
        <div class="form-group">
          <label>المهنة في التأمينات</label>
          <input type="text" name="insurance_job" />
        </div>
        <div class="form-group">
          <label>راتب التأمينات</label>
          <input type="text" name="insurance_salary" />
        </div>
        <div class="form-group">
          <label>ما يتحمله العامل</label>
          <input type="text" name="worker_cost" />
        </div>
        <div class="form-group">
          <label>ما تتحمله الشركة</label>
          <input type="text" name="company_cost" />
        </div>
        <div class="form-group">
          <label>الأجر الشامل</label>
          <input type="text" name="total_salary" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>رقم البطاقة الصحية</label>
          <input type="text" name="health_card" />
        </div>
        <div class="form-group">
          <label>قياس المهارة</label>
          <input type="text" name="skill_level" />
        </div>
        <div class="form-group">
          <label>تاريخ بداية قياس المهارة <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="skill_start" />
        </div>
        <div class="form-group">
          <label>تاريخ انتهاء قياس المهارة <i class="fa-regular fa-calendar-days"></i></label>
          <input type="date" name="skill_end" />
        </div>
        <div class="form-group">
          <label>الوقت المتبقى على انتهاء قياس المهارة</label>
          <input type="text" name="skill_remaining" />
        </div>
        <div class="form-group">
          <label>مهنة قياس المهارة</label>
          <input type="text" name="skill_job" />
        </div>
        <hr class="form-separator">
        <div class="form-group">
          <label>رصيد الإجازات</label>
          <input type="text" name="leave_balance" />
        </div>
        <div class="form-group">
          <label>الإجازات المستخدمة</label>
          <input type="text" name="leave_used" />
        </div>
        <div class="form-group">
          <label>الإجازات المتبقية</label>
          <input type="text" name="leave_remaining" />
        </div>
        <div class="form-group">
          <label>ذوي الهمم</label>
          <input type="text" name="special_needs" />
        </div>

      </div>
      <button type="submit" class="submit-btn">إضافة</button>
    </form>
  </div>

  <!-- نافذة إضافة إدارة جديدة -->
  <div id="addDepartmentModal" class="modal" style="display: none;">
    <div class="modal-content">
      <span class="close" id="closeDepartmentModal">&times;</span>
      <h3>إضافة إدارة جديدة</h3>
      <form id="addDepartmentForm">
        <div class="form-group">
          <label for="newDepartmentName">اسم الإدارة الجديدة:</label>
          <input type="text" id="newDepartmentName" required placeholder="أدخل اسم الإدارة">
        </div>
        <div class="modal-actions">
          <button type="submit" class="btn-primary">إضافة</button>
          <button type="button" class="btn-secondary" id="cancelDepartmentModal">إلغاء</button>
        </div>
      </form>
    </div>
  </div>

  <script src="arabic-date-picker.js"></script>
  <script src="dateUtils.js"></script>
  <script>
    // تحميل الأقسام والكود التلقائي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', async function() {

      await loadDepartments();
      await loadNextEmployeeCode();
      setupDepartmentModal();
    });

    // وظيفة تحميل الكود التلقائي للموظف
    async function loadNextEmployeeCode() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('http://localhost:5500/api/employees/next-code', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const data = await response.json();
          console.log('تم تحميل الكود التالي:', data.nextCode);
          document.getElementById('employeeCode').value = data.nextCode;
        } else {
          console.error('فشل في تحميل الكود التلقائي - رمز الاستجابة:', response.status);
          // محاولة الحصول على آخر كود من جدول الموظفين
          await loadNextCodeFromEmployees();
        }
      } catch (error) {
        console.error('خطأ في تحميل الكود التلقائي:', error);
        // محاولة الحصول على آخر كود من جدول الموظفين
        await loadNextCodeFromEmployees();
      }
    }
    
    // وظيفة بديلة للحصول على آخر كود من جدول الموظفين
    async function loadNextCodeFromEmployees() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('http://localhost:5500/api/employees', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (response.ok) {
          const employees = await response.json();
          let maxCode = 0;
          employees.forEach(emp => {
            const code = parseInt(emp.code);
            if (!isNaN(code) && code > maxCode) {
              maxCode = code;
            }
          });
          const nextCode = maxCode + 1;
          console.log('تم حساب الكود التالي من جدول الموظفين:', nextCode);
          document.getElementById('employeeCode').value = nextCode.toString();
        } else {
          console.error('فشل في تحميل بيانات الموظفين');
          document.getElementById('employeeCode').value = '1';
        }
      } catch (error) {
        console.error('خطأ في تحميل بيانات الموظفين:', error);
        document.getElementById('employeeCode').value = '1';
      }
    }

    // تحميل قائمة الأقسام
    async function loadDepartments() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('http://localhost:5500/api/employees/departments', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        const departments = await response.json();

        const departmentSelect = document.getElementById('departmentSelect');
        departmentSelect.innerHTML = '<option value="">اختر الإدارة</option>';

        departments.forEach(dept => {
          const option = document.createElement('option');
          option.value = dept;
          option.textContent = dept;
          departmentSelect.appendChild(option);
        });
        
        // إضافة خيار "إدارة جديدة"
        const newDeptOption = document.createElement('option');
        newDeptOption.value = 'new_department';
        newDeptOption.textContent = '+ إدارة جديدة';
        newDeptOption.style.fontWeight = 'bold';
        newDeptOption.style.color = '#007bff';
        departmentSelect.appendChild(newDeptOption);
        
      } catch (error) {
        console.error('خطأ في تحميل الأقسام:', error);
      }
    }

    // إعداد النافذة المنبثقة للإدارة
    function setupDepartmentModal() {
      const departmentSelect = document.getElementById('departmentSelect');
      const modal = document.getElementById('addDepartmentModal');
      const closeBtn = document.getElementById('closeDepartmentModal');
      const cancelBtn = document.getElementById('cancelDepartmentModal');
      const form = document.getElementById('addDepartmentForm');
      
      // عند اختيار "إدارة جديدة"
      departmentSelect.addEventListener('change', function() {
        if (this.value === 'new_department') {
          modal.style.display = 'block';
          document.getElementById('newDepartmentName').focus();
        }
      });
      
      // إغلاق النافذة
      function closeModal() {
        modal.style.display = 'none';
        departmentSelect.value = '';
        document.getElementById('newDepartmentName').value = '';
      }
      
      closeBtn.addEventListener('click', closeModal);
      cancelBtn.addEventListener('click', closeModal);
      
      // إغلاق عند النقر خارج النافذة
      window.addEventListener('click', function(event) {
        if (event.target === modal) {
          closeModal();
        }
      });
      
      // إضافة إدارة جديدة
      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        const newDepartmentName = document.getElementById('newDepartmentName').value.trim();

        if (!newDepartmentName) {
          alert('يرجى إدخال اسم الإدارة');
          return;
        }

        try {
          // إضافة الإدارة إلى قاعدة البيانات
          const token = localStorage.getItem('token');
          const response = await fetch('http://localhost:5500/api/department-permissions/departments', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              name: newDepartmentName,
              description: `إدارة ${newDepartmentName}`
            })
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.message || 'فشل في إضافة الإدارة');
          }

          // إضافة الإدارة الجديدة إلى القائمة
          const option = document.createElement('option');
          option.value = newDepartmentName;
          option.textContent = newDepartmentName;

          // إدراج الخيار الجديد قبل "إدارة جديدة"
          const newDeptOption = departmentSelect.querySelector('option[value="new_department"]');
          departmentSelect.insertBefore(option, newDeptOption);

          // اختيار الإدارة الجديدة
          departmentSelect.value = newDepartmentName;

          closeModal();
          alert('تمت إضافة الإدارة بنجاح');

        } catch (error) {
          console.error('خطأ في إضافة الإدارة:', error);
          alert('فشل في إضافة الإدارة: ' + error.message);
        }
      });
    }

    // إرسال نموذج إضافة الموظف
    document.getElementById("addEmployeeForm").addEventListener("submit", async function(e) {
      e.preventDefault();

      // التحقق من اختيار إدارة صحيحة
      const departmentValue = document.getElementById('departmentSelect').value;
      if (departmentValue === 'new_department' || !departmentValue) {
        alert('يرجى اختيار إدارة صحيحة');
        return;
      }

      try {
        // إنشاء البيانات الأساسية (بدون الملفات)
        const formData = new FormData(e.target);
        const data = {};
        formData.forEach((value, key) => {
          data[key] = value;
        });

        // إرسال بيانات الموظف أولاً
        const token = localStorage.getItem('token');
        const employeeResponse = await fetch("http://localhost:5500/api/employees", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(data)
        });

        if (!employeeResponse.ok) {
          const errorData = await employeeResponse.json();
          throw new Error(errorData.error || "فشل في إضافة الموظف");
        }

        const employeeResult = await employeeResponse.json();
        const employeeCode = employeeResult.code || data.code;

        // تم إزالة كود رفع الصور والمستندات لأن الحقول تم تحويلها إلى تواريخ العقد

        alert("تمت إضافة الموظف بنجاح");

        // تحديث الكود للموظف التالي
        loadNextEmployeeCode();

        // إعادة تعيين النموذج
        document.getElementById("addEmployeeForm").reset();
        document.getElementById("contract_remaining").value = "";

        // إعادة تحميل الأقسام
        loadDepartments();

      } catch (err) {
        console.error("خطأ في إضافة الموظف:", err);
        alert("فشل في إضافة الموظف: " + err.message);
      }
    });
  </script>

  <!-- JavaScript لحساب المدة المتبقية من العقد -->
  <script>
    // دالة لحساب المدة المتبقية من العقد
    function calculateContractRemaining(endDate, startDate) {
      // إذا لم يتم تمرير تاريخ البداية، استخدم تاريخ بداية العقد من النموذج
      if (!startDate) {
        const contractStartInput = document.querySelector('input[name="contract_start_date"]');
        startDate = contractStartInput ? contractStartInput.value : null;
      }

      // استخدام الدالة المحسنة من dateUtils.js إذا كانت متوفرة
      if (typeof DateUtils !== 'undefined' && DateUtils.calculateContractDuration) {
        const result = DateUtils.calculateContractDuration(startDate, endDate);
        return result.formattedText;
      }

      // Fallback للحالات التي لا تتوفر فيها dateUtils.js
      if (!endDate) {
        return "";
      }

      const today = new Date();
      const contractEnd = new Date(endDate);

      // إذا كان تاريخ انتهاء العقد في الماضي
      if (contractEnd < today) {
        return "انتهى العقد";
      }

      // حساب الفرق بالأيام
      const diffTime = contractEnd - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return `المتبقي: ${diffDays} يوم`;
    }

    // إضافة مستمع للأحداث لحقول تاريخ العقد
    document.addEventListener('DOMContentLoaded', function() {
      const contractEndInput = document.getElementById('contract_end_date');
      const contractStartInput = document.querySelector('input[name="contract_start_date"]');
      const contractRemainingInput = document.getElementById('contract_remaining');

      function updateContractRemaining() {
        if (contractEndInput && contractRemainingInput) {
          const startDate = contractStartInput ? contractStartInput.value : null;
          const endDate = contractEndInput.value;
          const remaining = calculateContractRemaining(endDate, startDate);
          contractRemainingInput.value = remaining;
        }
      }

      if (contractEndInput && contractRemainingInput) {
        contractEndInput.addEventListener('change', updateContractRemaining);
      }

      if (contractStartInput && contractRemainingInput) {
        contractStartInput.addEventListener('change', updateContractRemaining);
      }

      // حساب المدة عند تحميل الصفحة إذا كان هناك قيمة
      if (contractEndInput && contractEndInput.value) {
        updateContractRemaining();
      }
    });
  </script>


</body>
</html>
